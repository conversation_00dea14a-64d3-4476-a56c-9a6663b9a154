"use client";
import React, { useState } from "react";
import { ReloadIcon } from "@radix-ui/react-icons";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { DeleteRequest } from "~/utils/request";
import cogoToast from "cogo-toast";
import { Trash2 } from "lucide-react";

interface DeleteAdminModalProps {
  isOpen?: boolean;
  onClose: () => void;
  userId?: string;
}

export const DeleteAdminModal: React.FC<DeleteAdminModalProps> = ({
  isOpen,
  onClose,
  userId,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const deleteAccount = async () => {
    const token = localStorage.getItem("admintoken");
    if (!token) {
      return;
    }
    setLoading(true);

    const response = await DeleteRequest(`/backoffice/admins/${userId}`, token);

    if (response?.status === 200 || response?.status === 201) {
      cogoToast.success(response?.data?.message);
      setTimeout(() => {
        setSuccess(true);
      }, 1000);
      setLoading(false);
    } else {
      setLoading(false);
    }
    setTimeout(() => {
      onClose();
    }, 3000);
  };

  // const handleClose = async () => {
  //   setSuccess(false);
  // };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] rounded-[6px] max-w-[95%]">
        {success ? (
          <></>
        ) : (
          // <SucessModalContent
          //   onClick={handleClose}
          //   text="This account has been deleted"
          // />
          <>
            <DialogHeader>
              <DialogTitle className="text-lg text-left">
                Delete Account
              </DialogTitle>
              <DialogDescription className="text-black text-sm font-normal leading-7">
                Are you sure you want to permanently delete this account? This
                action cannot be undone and all this account data will be lost
                forever.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="flex flex-row gap-4 justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="border-#E2E8F0 text-[#0F172A] hover:bg-red-50"
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="destructive"
                onClick={deleteAccount}
                disabled={loading}
                className="bg-gradient-to-b from-red-600 to-red-600 hover:bg-gradient-to-b hover:from-red-700 hover:to-red-700"
              >
                {loading ? (
                  <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                ) : null}
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
