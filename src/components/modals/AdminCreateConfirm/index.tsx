"use client";
import React from "react";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
} from "~/components/ui/dialog";
import Image from "next/image";

interface AdminCreateConfirmProps {
  isOpen?: boolean;
  onClose: () => void;
  email?: string;
  password?: string;
}

export const AdminCreateConfirm = (props: AdminCreateConfirmProps) => {
  return (
    <Dialog open={props.isOpen} onOpenChange={props.onClose}>
      <DialogContent className="sm:max-w-[425px] rounded-[6px] max-w-[95%]">
        <div className="flex flex-col items-center gap-4 py-2">
          <Image
            src={"/images/confirmation-icon.svg"}
            width={60}
            height={60}
            alt=""
          />
          <DialogDescription className="text-[#0A0A0A] text-center text-lg font-semibold font-inter">
            Success
            <p className="text-[#475569] text-sm font-normal leading-7">
              User has been added successfully
            </p>
          </DialogDescription>
          <DialogFooter className="w-full">
            <Button
              className="w-full bg-gradient-to-b from-[#8760f8] to-[#7141f8] text-white py-3 px-4 hover:opacity-90"
              onClick={props.onClose}
            >
              Close
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};
