"use client";
import React, { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import Loading from "~/components/ui/loading";
import cogoToast from "cogo-toast";
import { PostRequest } from "~/utils/request";
// import { AdminCreateConfirm } from "~/components/modals/AdminCreateConfirm";

interface ComponentProps {
  closeModal: () => void;
}

export const CreateAdminModal = (props: ComponentProps) => {
  const [loading, setLoading] = useState(false);
  const [token, setToken] = useState("");
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");

  useEffect(() => {
    const s_token = localStorage.getItem("admintoken") || "";
    setToken(s_token);
  }, [token]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!email || !name) {
      cogoToast.error("Please fill all the required fields");
      return;
    }

    setLoading(true);

    const payload = {
      name,
      email,
      role: "admin",
    };

    const response = await PostRequest(`/backoffice/admins`, payload, token);

    console.log(response);

    if (response?.status === 200 || response?.status === 201) {
      props.closeModal();
      cogoToast.success("User added successfully");
    }

    setLoading(false);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  return (
    <>
      <div className="w-screen h-screen fixed top-0 right-0 flex items-center z-10  justify-center">
        <div
          onClick={() => props.closeModal()}
          className="w-full h-full absolute bg-black opacity-20"
        ></div>
        <div className="bg-white w-[400px] lg:w-[600px] flex flex-col rounded-xl  z-10 gap-8">
          <div className="border-b border-gray-200 p-6 pb-4">
            <h1 className="text-[#1D2939] lg:text-xl text-lg font-semibold leading-normal">
              Create User
            </h1>
          </div>

          <form onSubmit={handleSubmit} className="w-full px-5">
            <div className="flex flex-col gap-[16px] mb-3">
              <div className="w-full flex flex-col gap-[8px] relative">
                <label
                  htmlFor="name"
                  className="text-[14px] font-[400] leading-[21px]"
                >
                  Fullname
                </label>
                <div className="w-full flex flex-col gap-[2px]">
                  <input
                    value={name}
                    onChange={handleNameChange}
                    id="name"
                    placeholder="Enter Fullname"
                    className={`w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]
                                        outline-none rounded-md py-[13px] pl-[13px]`}
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-[16px] mb-3">
              <div className="w-full flex flex-col gap-[8px] relative">
                <label
                  htmlFor="email"
                  className="text-[14px] font-[400] leading-[21px]"
                >
                  Email Address
                </label>
                <div className="w-full flex flex-col gap-[2px]">
                  <input
                    value={email}
                    onChange={handleEmailChange}
                    id="email"
                    type="email"
                    placeholder="Enter email address"
                    className={`w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]
                                        outline-none rounded-md py-[13px] pl-[13px]`}
                  />
                </div>
              </div>
            </div>

            {/* <div className="flex flex-col gap-[16px] mb-3">
            <div className="w-full flex flex-col gap-[8px] relative">
              <label
                htmlFor="category"
                className="text-[14px] font-[400] leading-[21px]"
              >
                Category
              </label>
              <div className="w-full flex flex-col gap-[2px]">
                <Select
                  value={category}
                  onValueChange={(value) => {
                    setCategory(value);
                  }}

                >
                  <SelectTrigger className="w-full h-[42px]">
                    <SelectValue
                      placeholder=" Select user category"
                      className="text-gray-800 !placeholder:text-gray-400"
                    >
                      Select user category
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      value="user"
                      className="py-1 px-2 cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-gray-100 data-[state=checked]:font-medium"
                    >
                      User
                    </SelectItem>
                    <SelectItem
                      value="admin"
                      className="py-1 px-2 cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-gray-100 data-[state=checked]:font-medium"
                    >
                      Admin
                    </SelectItem>
                    <SelectItem
                      value="superadmin"
                      className="py-1 px-2 cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-gray-100 data-[state=checked]:font-medium"
                    >
                      Super-Admin
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div> */}

            <div className="flex flex-col gap-[24px] mt-5 mb-10">
              <div className="flex flex-col gap-[16px]">
                <Button
                  type="submit"
                  variant="default"
                  className="py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white"
                >
                  {loading ? (
                    <span className="flex items-center gap-x-2">
                      <span className="animate-pulse">Processing...</span>{" "}
                      <Loading width="20" height="40" />
                    </span>
                  ) : (
                    <span>Create User</span>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};
