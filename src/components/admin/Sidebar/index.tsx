"use client";

import {
  Home,
  Bot,
  Settings,
  User,
  DollarSign,
  BriefcaseBusiness,
  LogOut,
} from "lucide-react";
import React, { useContext } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";

interface ButtonData {
  id: string;
  icon: React.ComponentType<any>;
  label: string;
  link: string;
}

const SideBar: React.FC = () => {
  const pathname = usePathname();
  const { state, dispatch } = useContext(DataContext);

  const buttonData: ButtonData[] = [
    {
      id: "home",
      icon: Home,
      label: "Dashboard",
      link: "/admin/dashboard",
    },
    {
      id: "agents",
      icon: Bot,
      label: "Agents",
      link: "/admin/agents",
    },
    {
      id: "organizations",
      icon: BriefcaseBusiness,
      label: "Organizations",
      link: "/admin/organizations",
    },
    {
      id: "credits_report",
      icon: DollarSign,
      label: "Credits Report",
      link: "/admin/credits-report",
    },
    {
      id: "users",
      icon: User,
      label: "Users",
      link: "/admin/users",
    },
    {
      id: "settings",
      icon: Settings,
      label: "Settings",
      link: "/admin/settings",
    },
    {
      id: "logout",
      icon: LogOut,
      label: "Log Out",
      link: "",
    },
  ];

  return (
    <div
      className={`fixed top-0 z-50 md:z-0 md:translate-x-0 transition-transform duration-300 ease-in-out
               ${state?.openSidebar === true ? "translate-x-0" : "-translate-x-40"}`}
    >
      <div
        className={`h-[100vh] w-[250px] p-[8px] -z-50
        md:flex flex-col justify-start`}
      >
        <div className="h-full w-full text-gray-200 rounded-[12px] z-auto bg-[#7b50fb] p-4">
          <div className="relative">
            <div className="border-b text-[15px] border-gray-200 pb-5 mt-4 flex flex-col items-center text-center">
              <Link
                href=""
                className="flex flex-col items-center group mb-2 space-y-1 w-full p-[5px] font-semibold scale-[1.1]"
              >
                <span className="bg-[#7b50fb] p-[4px] rounded-[7px] flex items-center justify-center">
                  <User
                    strokeWidth={1.2}
                    className="fill-white stroke-[#7b50fb]"
                  />
                </span>
              </Link>

              <p className="font-bold">Ganiu Jamiu</p>
              <p><EMAIL></p>
            </div>

            <div
              className="flex flex-col items-center mt-6 w-full text-[12px]"
              onClick={() =>
                dispatch({ type: ACTIONS.OPEN_SIDEBAR, payload: false })
              }
            >
              {buttonData.map((button) => {
                const isActive =
                  button.id === "home"
                    ? pathname === "/admin/dashboard"
                    : pathname.includes(button.id);
                return (
                  <Link
                    href={button.link}
                    key={button.id}
                    className={`flex flex-row group mb-2 space-y-1 w-full p-[5px] ${
                      isActive ? "font-semibold" : "hover:font-semibold "
                    }`}
                  >
                    <span
                      className={`p-[4px] rounded-[7px]  ${
                        isActive ? "bg-[#7b50fb]" : "group-hover:bg-[#7b50fb]"
                      }`}
                    >
                      <button.icon
                        strokeWidth={1.2}
                        className={`${
                          isActive
                            ? "fill-white stroke-[#7b50fb]"
                            : "group-hover:fill-white group-hover:stroke-[#7b50fb]"
                        }`}
                      />
                    </span>
                    <span className="text-[12px] ml-3">{button.label}</span>
                  </Link>
                );
              })}
              ;
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideBar;
