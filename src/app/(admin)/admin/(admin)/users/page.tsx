"use client";
import { useState, useEffect } from "react";
import { GetRequest } from "~/utils/request";
import { Clock, CheckCircle } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";
import Icons from "~/app/(client)/client/_components/billing/icons";

import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuRadioGroup,
} from "~/components/ui/dropdown-menu";

import { Search, RefreshCw, Filter, Edit, Trash } from "lucide-react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { RefreshEntities, getStatusColor } from "~/utils/utils";
import { CreateAdminModal } from "~/components/modals/CreateAdminModal";
import { DeleteAdminModal } from "~/components/modals/DeleteAdminModal";
import LoadingState from "../component/loadindState";
import EmptyState from "../component/emptyState";

function Users() {
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isRotating, setIsRotating] = useState(false);
  const [users, setUsers] = useState<any>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeletedModalOpen, setIsDeletedModalOpen] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [userID, setUserID] = useState("");

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  var totalPages = 10;
  var currentPage = 1;

  const fetchUsers = async () => {
    const token: any = localStorage.getItem("admintoken");

    try {
      const response = await GetRequest(`/backoffice/admins`, token);
      const result = response.data.data;

      setUsers(result);
    } catch (error) {
      console.log(error);
    }
    setIsPageLoading(false);
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const closeAdminModal = () => {
    setIsDeletedModalOpen(false);
  };

  const RefreshUsers = async () => {
    RefreshEntities(setIsRotating);
    await fetchUsers();
  };

  const openAdminModal = (id: string) => {
    setIsDeletedModalOpen(true);
    setUserID(id);
  };

  return (
    <>
      {isModalOpen && <CreateAdminModal closeModal={closeModal} />}

      {isDeletedModalOpen && (
        <DeleteAdminModal
          isOpen={isDeletedModalOpen}
          onClose={closeAdminModal}
          userId={userID}
        />
      )}

      <div className="mx-auto xl:max-w-[1440px] grow max-w-screen">
        <div className="mt-5 p-4">
          <h2 className="font-bold text-[20px]">Users</h2>
          <p className="text-gray-600 text-[14px]">
            Welcome to Your Agents Dashboard — View, Manage, and Monitor All
            Agents in One Place
          </p>
        </div>

        <div className="flex justify-between mb-5 p-4">
          <div className="flex items-center relative w-[500px]">
            <Search className="absolute left-3 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 pl-9 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#7b50fb]"
            />
          </div>

          <div className="flex flex-row">
            <div className="mr-5">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    {filter}
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent side="bottom" align="start">
                  <DropdownMenuLabel>Filter Agents</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={filter}
                    onValueChange={(value) => setFilter(value)}
                  >
                    <DropdownMenuRadioItem value="All">
                      All
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="Active">
                      Active
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="Inactive">
                      Inactive
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <button
              onClick={RefreshUsers}
              aria-label="RefreshCw"
              className="py-1 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition"
            >
              <RefreshCw
                className={`h-4 w-4 text-gray-500 transition-transform ${
                  isRotating ? "animate-spin" : ""
                }`}
              />
            </button>
            <button
              onClick={openModal}
              className="py-1 ml-5 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100"
            >
              Add User
            </button>
          </div>
        </div>
        {!isPageLoading && users.length > 0 && (
          <div className="flex flex-col h-screen justify-between">
            <div className="bg-white ">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fullname</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users?.map((user: any, index: number) => (
                    <TableRow
                      key={index}
                      className="hover:bg-gray-50 transition-colors"
                    >
                      <TableCell>{user.name}</TableCell>
                      <TableCell>{user.role.toLocaleUpperCase()}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <div
                          className={`flex items-center space-x-1 px-3 py-1 w-[100px] rounded-full border ${getStatusColor("active")}`}
                        >
                          {getStatusIcon("active")}
                          <span className="capitalize font-medium">Active</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <button
                          onClick={() => {}}
                          className="p-1 mx-2 border rounded-md text-gray-500 hover:bg-gray-100 transition"
                        >
                          <Edit className="w-4 h-4" />
                        </button>

                        <button
                          onClick={() => openAdminModal(user.id)}
                          className="p-1 border rounded-md text-gray-500 hover:bg-gray-100 transition"
                        >
                          <Trash className="w-4 h-4" color="#ff0000" />
                        </button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center pb-10 mb-10 justify-between px-6 space-x-2 py-4">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-300"
              >
                <Icons name="move-left" svgProps={{}} />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                  let pageNum: number;
                  if (totalPages <= 7) {
                    pageNum = i + 1;
                  } else if (currentPage <= 4) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 3) {
                    pageNum = totalPages - 6 + i;
                  } else {
                    pageNum = currentPage - 3 + i;
                  }

                  if (pageNum === 4 && currentPage > 4 && totalPages > 7) {
                    return (
                      <span key="ellipsis1" className="px-2">
                        ...
                      </span>
                    );
                  }
                  if (
                    pageNum === totalPages - 3 &&
                    currentPage < totalPages - 3 &&
                    totalPages > 7
                  ) {
                    return (
                      <span key="ellipsis2" className="px-2">
                        ...
                      </span>
                    );
                  }

                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      className="w-8 h-8 p-0"
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-300"
              >
                Next
                <Icons name="move-right" svgProps={{}} />
              </Button>
            </div>
          </div>
        )}

        {isPageLoading && users.length == 0 ? (
          <LoadingState />
        ) : (
          <EmptyState
            title="Empty Users"
            msg="No user has been added at the moment"
          />
        )}
      </div>
    </>
  );
}

export default Users;
