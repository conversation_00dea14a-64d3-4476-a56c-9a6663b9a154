"use client";
import { useState, useEffect } from "react";
import { GetRequest, DeleteRequest, PutRequest } from "~/utils/request";
import Image from "next/image";
import {
  Package,
  Clock,
  CheckCircle,
  Trash,
  Edit,
  Eye,
  EyeOff,
  Copy,
} from "lucide-react";
import { getStatusColor } from "~/utils/utils";
import { useParams } from "next/navigation";
import LoadingState from "../../../component/loadindState";
import cogoToast from "cogo-toast";

function Agent() {
  const [agent, setAgent] = useState<any>([]);
  const [user, setUser] = useState<any>([]);
  const [credit, setCredit] = useState(0);
  const [agentEarnings, setAgentEarnings] = useState(0);
  const [telexEarnings, setTelexEarnings] = useState(0);
  const params = useParams();
  const [showKey, setShowKey] = useState(false);
  const source = params.id as string;
  const id = params.id2 as string;
  const [copied, setCopied] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [isAgentFound, setIsAgentFound] = useState(false);
  const [isSystem, setIsSystem] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [isApproved, setIsApproved] = useState(false);

  const handleToggleActive = async () => {
    setIsActive(!isActive);
    await UpdateAgent(!isActive, isSystem, isApproved);
  };

  const handleToggleSystem = async () => {
    setIsSystem(!isSystem);
    await UpdateAgent(isActive, !isSystem, isApproved);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(agent?.preshared_key);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const DeleteAgent = async () => {
    const token: any = localStorage.getItem("admintoken");

    try {
      await DeleteRequest(`/backoffice/agents/${id}`, token);
      cogoToast.success("Agent deleted successfully");

      window.location.href = "/admin/agents";
    } catch (error) {
      console.log(error);
    }
  };

  const UpdateAgent = async (
    is_active: boolean,
    is_system: boolean,
    is_approved: boolean
  ) => {
    const token: any = localStorage.getItem("admintoken");

    const payload = {
      is_system,
      is_active,
      is_approved,
    };

    console.log(payload);

    try {
      const response = await PutRequest(
        `/backoffice/agents/${id}`,
        payload,
        token
      );
      console.log(response);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchAgents();
  }, []);

  const fetchAgents = async () => {
    setIsPageLoading(true);
    setIsAgentFound(false);
    const token: any = localStorage.getItem("admintoken");

    try {
      const response = await GetRequest(
        `/backoffice/agents/${source}/${id}`,
        token
      );
      console.log(response);
      const result = response.data.data;

      setAgent(result.agent);
      setUser(result.user);
      setCredit(result.credit_used);
      setIsAgentFound(true);

      setIsSystem(result.agent.is_system);
      setIsActive(result.agent.is_active);
      setIsApproved(result.agent.is_approved);

      setAgentEarnings(result.maker_total_earning);
      setTelexEarnings(result.telex_total_earning);
    } catch (error) {
      console.log(error);
      setIsAgentFound(false);
    }
    setIsPageLoading(false);
  };

  return (
    <>
      <div className="mx-auto xl:max-w-[1440px] grow max-w-screen">
        <div className="mt-5 p-4">
          <h2 className="font-bold text-[20px]">Agent Informations</h2>
          <p className="text-gray-600 text-[14px]">
            Welcome to Your Agents Dashboard — View, Manage, and Monitor All
            Agents in One Place
          </p>
        </div>

        {isPageLoading && <LoadingState />}

        {!isPageLoading && isAgentFound ? (
          <div className="p-4 mt-10 bg-white rounded-md border border-gray-100">
            <div className="flex items-center mt-3 mb-5">
              <p className="flex-1">
                API Key:{" "}
                {showKey ? agent?.preshared_key : "••••••••••••••••••••••••"}
              </p>
              <button
                onClick={() => setShowKey((prev) => !prev)}
                aria-label="Toggle API key visibility"
                className="p-1 ml-2"
              >
                {showKey ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
              <button
                onClick={handleCopy}
                aria-label="Copy API key to clipboard"
                className="p-1 ml-2"
              >
                {copied ? (
                  <Copy size={20} color="#4ade80" />
                ) : (
                  <Copy size={20} />
                )}
              </button>
            </div>

            <div className="flex flex-row justify-between">
              <div className="flex flex-row">
                <div className="flex items-center space-x-1">
                  <div className="p-2 bg-solar-green-100 rounded-full">
                    <Package className="h-5 w-5 text-solar-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold">{agent.app_name}</h3>
                  </div>
                </div>
                <p className="text-[12px] text-gray-500 mt-3 ml-2">- Free</p>
              </div>

              <div className="flex flex-row">
                <button
                  onClick={DeleteAgent}
                  aria-label="RefreshCw"
                  className="py-1 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition"
                >
                  <Trash
                    color="#ff0000"
                    className={`h-4 w-4 text-gray-500 transition-transform`}
                  />
                </button>
                <button
                  aria-label="RefreshCw"
                  className="py-1 px-4 ml-3 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition"
                >
                  <Edit
                    color="#007bff"
                    className={`h-4 w-4 text-gray-500 transition-transform`}
                  />
                </button>
              </div>
            </div>

            <div className="flex flew-row space-x-2">
              <button
                onClick={handleToggleActive}
                className={`flex items-center text-[14px] mb-5 px-4 space-x-1 px-3 py-1 rounded-full border shadow-md transition-colors duration-300 ${
                  isActive
                    ? getStatusColor("active")
                    : getStatusColor("inactive")
                }`}
              >
                {isActive ? (
                  <>
                    {getStatusIcon("active")}
                    <span className="capitalize font-medium">Active</span>
                  </>
                ) : (
                  <>
                    {getStatusIcon("inactive")}
                    <span className="capitalize font-medium">inactive</span>
                  </>
                )}
              </button>

              <button
                onClick={handleToggleSystem}
                className={`flex items-center text-[14px] mb-5 px-4 space-x-1 px-3 py-1 rounded-full border shadow-md transition-colors duration-300 ${
                  isSystem ? "bg-blue-300 text-white" : "bg-red-200"
                }`}
              >
                {isSystem ? (
                  <>
                    <span className="capitalize font-medium">System Agent</span>
                  </>
                ) : (
                  <>
                    <span className="capitalize font-medium">
                      Non-System Agent
                    </span>
                  </>
                )}
              </button>
            </div>

            <div className="mb-3 flex flex-row space-x-2">
              <p className="text-gray-500">Creator: </p>
              <p className="font-medium">
                {user?.name} -{" "}
                <span className="text-gray-400 text-[14px]">{user?.email}</span>
              </p>
            </div>

            <div className="mb-3 flex flex-row space-x-2">
              <p className="text-gray-500">Total Credits used: </p>
              <p className="font-medium">{credit} Credit(s)</p>
            </div>
            <div className="mb-3 flex flex-row space-x-2">
              <p className="text-gray-500">Agent Earnings: </p>
              <p className="font-medium">{agentEarnings} Credit(s)</p>
            </div>
            <div className="mb-3 flex flex-row space-x-2">
              <p className="text-gray-500">Telex Earnings: </p>
              <p className="font-medium">{telexEarnings} Credit(s)</p>
            </div>

            <div className="mb-3 flex flex-row space-x-2">
              <p className="text-gray-500">Date Created: </p>
              <p className="font-medium">
                {new Date(agent?.created_at).toLocaleString("en-US", {
                  month: "long",
                  day: "2-digit",
                  year: "numeric",
                })}
              </p>
            </div>

            <div className="mb-3 flex flex-row space-x-2">
              <p className="text-gray-500">Provider</p>
              <p className="font-medium">
                {agent?.provider?.organization
                  ? agent?.provider?.organization
                  : "Unknown"}
              </p>
            </div>

            <div className="border-b border-gray-200 mt-5 pb-5">
              <p className="text-gray-500">Description</p>
              <p className="">{agent?.app_description}</p>
            </div>

            <div className="border-b border-gray-200 mt-5 pb-5">
              <p className="text-gray-500">Agent Prices</p>
              {agent?.prices?.map((price: any, index: number) => {
                return (
                  <div key={index} className="space-y-4">
                    <p className="text-base mt-5">
                      <span>Amount</span>: {price.amount}
                    </p>
                    <p className="text-base">
                      <span>Currency Code</span>: {price.currency}
                    </p>
                    <p className="text-base">
                      <span>Operation Type</span>: {price.operation_type}
                    </p>
                  </div>
                );
              })}
            </div>

            <div className="border-b border-gray-200 mt-5 pb-5 mb-5">
              <p className="text-gray-500">Agent Skills</p>
              {agent?.skills?.map((skill: any, index: number) => {
                return (
                  <div key={index} className="space-y-4">
                    <p className="text-base mt-5">
                      <span>Name</span>: {skill?.name}
                    </p>
                    <p className="text-base">
                      <span>Tags</span>: {skill?.tags?.join(", ")}
                    </p>
                    <p className="text-base">
                      <span>Description</span>: {skill?.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="w-full h-screen flex flex-col">
            <div className="flex flex-col items-center justify-center md:p-10 mt-6">
              <div className="relative">
                <Image
                  src="/image/empty-agent.svg"
                  height="100"
                  width="100"
                  alt="No app yet image"
                  className="w-30 md:w-[150px] h-64"
                />
              </div>
              <p className="mt-[40px] md:flex text-xl md:text-2xl text-center font-semibold leading-8">
                Agent not found
              </p>
              <p className="w-[300px] md:w-[481px] text-center text-sm font-light leading-6">
                Ooops!, it seems this agent does not exist
              </p>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

export default Agent;
