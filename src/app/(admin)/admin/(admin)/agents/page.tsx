"use client";
import React, { useState, useEffect } from "react";
import { GetRequest } from "~/utils/request";
import Link from "next/link";
import { Package, Clock, CheckCircle } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuRadioGroup,
} from "~/components/ui/dropdown-menu";
import { Search, RefreshCw, Filter, SortAsc } from "lucide-react";
import { RefreshEntities, getStatusColor } from "~/utils/utils";
import LoadingState from "../component/loadindState";
import EmptyState from "../component/emptyState";
import { ArrowRight, ArrowLeft } from "lucide-react";
import { CreateSystemAgentModal } from "~/components/modals/CreateSystemAgentModal";

function getPaginationRange(currentPage: number, totalPages: number) {
  const range = [];
  const delta = 2;

  const start = Math.max(2, currentPage - delta);
  const end = Math.min(totalPages - 1, currentPage + delta);

  range.push(1);
  if (start > 2) range.push("...");

  for (let i = start; i <= end; i++) {
    range.push(i);
  }

  if (end < totalPages - 1) range.push("...");
  if (totalPages > 1) range.push(totalPages);

  return range;
}

function Agents() {
  const [filter, setFilter] = useState("System Agent");
  const [sort, setSort] = useState("Date Created");
  const [searchTerm, setSearchTerm] = useState("");
  const [isRotating, setIsRotating] = useState(false);
  const [agents, setAgents] = useState<any>([]);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [isAgentsFound, setIsAgentsFound] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const fetchAgents = async () => {
    setIsPageLoading(true);
    const token: any = localStorage.getItem("admintoken");

    var is_system = true;

    if (filter == "system_agent") {
      is_system = true;
    } else if (filter == "organization_agent") {
      is_system = false;
    }

    var sortKey = "created_at";

    if (sort == "date_created") {
      sortKey = "created_at";
    } else if (sort == "credit_used") {
      sortKey = sort;
    }

    try {
      const response = await GetRequest(
        `/backoffice/agents/all?page=${currentPage}&is_system=${is_system}&search=${searchTerm}&sort_by=${sortKey}`,
        token
      );

      console.log(response);
      const result = response.data;

      setAgents(result.data);
      setIsAgentsFound(true);

      setCurrentPage(result.pagination[0].current_page);
    } catch (error) {
      console.log(error);
      setIsAgentsFound(false);
    }
    setIsPageLoading(false);
  };

  useEffect(() => {
    fetchAgents();
  }, [currentPage, filter, searchTerm, sort]);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setSearchTerm(inputValue);
    }
  };

  return (
    <>
      {isModalOpen && <CreateSystemAgentModal closeModal={closeModal} />}

      <div className="mx-auto xl:max-w-[1440px] grow max-w-screen">
        <div className="mt-5 p-4">
          <h2 className="font-bold text-[20px]">Custom Agents</h2>
          <p className="text-gray-600 text-[14px]">
            Welcome to Your Agents Dashboard — View, Manage, and Monitor All
            Agents in One Place
          </p>
        </div>

        <div className="flex justify-between mb-5 p-4">
          <div className="flex items-center relative w-[500px]">
            <Search className="absolute left-3 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search agents..."
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full p-2 pl-9 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#7b50fb]"
            />
          </div>
          <div className="flex flex-row space-x-2">
            <div className="">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4 text-gray-400" />
                    {filter
                      .replace(/_/g, " ")
                      .replace(/\b\w/g, (char) => char.toUpperCase())}
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent side="bottom" align="start">
                  <DropdownMenuLabel>Filter Agents</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={filter}
                    onValueChange={(value) => setFilter(value)}
                  >
                    <DropdownMenuRadioItem value="system_agent">
                      System Agents
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="organization_agent">
                      Organization Agents
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <SortAsc className="mr-2 h-4 w-4 text-gray-400" />
                    {sort
                      .replace(/_/g, " ")
                      .replace(/\b\w/g, (char) => char.toUpperCase())}
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent side="bottom" align="start">
                  <DropdownMenuLabel>Sort Agents</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={sort}
                    onValueChange={(value) => setSort(value)}
                  >
                    <DropdownMenuRadioItem value="date_created">
                      Date Created
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="credit_used">
                      Credit Used
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="most_used">
                      Most Earning
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <button
              onClick={() => {
                RefreshEntities(setIsRotating);
                fetchAgents();
              }}
              aria-label="RefreshCw"
              className="py-1 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition"
            >
              <RefreshCw
                className={`h-4 w-4 text-gray-500 transition-transform ${
                  isRotating ? "animate-spin" : ""
                }`}
              />
            </button>

            <button
              onClick={openModal}
              className="py-1 px-4 h-[37px] border text-white bg-blue-400 border-gray-200 rounded-md hover:bg-blue-600 transition"
            >
              Create System Agent
            </button>
          </div>
        </div>

        {isPageLoading && <LoadingState />}

        {!isPageLoading && isAgentsFound ? (
          <>
            <div className="p-5 bg-white rounded-md border border-gray-100">
              <div className="space-y-4">
                {agents?.map((agent: any, idx: number) => (
                  <Link
                    key={idx}
                    href={
                      agent.source == "organization"
                        ? `/admin/agents/organization/${agent?.integration_id}`
                        : `/admin/agents/integration/${agent?.id}`
                    }
                  >
                    <div className="p-6 border rounded-lg space-y-4 hover gap-2 hover:bg-gray-50 transition">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-1">
                          <div className="p-2 bg-solar-green-100 rounded-full">
                            <Package className="h-5 w-5 text-solar-green-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold">
                              {agent?.app_name || agent?.name}
                            </h3>
                          </div>
                        </div>
                        <div
                          className={`flex items-center space-x-1 px-3 py-1 rounded-full ${agent?.is_active ? getStatusColor("active") : getStatusColor("inactive")}`}
                        >
                          {agent?.is_active ? (
                            <>
                              {getStatusIcon("active")}
                              <span className="capitalize font-medium">
                                Active
                              </span>
                            </>
                          ) : (
                            <>
                              {getStatusIcon("inactive")}
                              <span className="capitalize font-medium">
                                inActive
                              </span>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-row space-x-2 pb-3">
                        <div
                          className={`flex items-center space-x-1 px-3 py-1 text-[10px] rounded-full ${agent?.source == "organization" ? "bg-amber-500 text-white" : "bg-gray-300"}`}
                        >
                          {agent?.source == "organization" ? (
                            <>
                              <span className="capitalize font-medium">
                                Organization Agent
                              </span>
                            </>
                          ) : (
                            <>
                              <span className="capitalize font-medium">
                                Non-Organization Agent
                              </span>
                            </>
                          )}
                        </div>

                        {/* <div
                          className={`flex items-center text-[10px] space-x-1 px-3 py-1 rounded-full ${agent?.is_approved ? "bg-green-300 text-green" : "bg-red-500 text-white"}`}
                        >
                          {agent?.is_approved ? (
                            <>
                              <span className="capitalize font-medium">
                                Approved
                              </span>
                            </>
                          ) : (
                            <>
                              <span className="capitalize font-medium">
                                Not Approved
                              </span>
                            </>
                          )}
                        </div> */}

                        <div
                          className={`flex items-center text-[10px] space-x-1 px-3 py-1 rounded-full bg-gray-100`}
                        >
                          <span className="capitalize font-medium">
                            Credits Used:{" "}
                            {agent?.credit_used ? agent?.credit_used : 0}{" "}
                            Credits
                          </span>
                        </div>

                        <div
                          className={`flex items-center text-[10px] space-x-1 px-3 py-1 rounded-full bg-amber-100`}
                        >
                          <span className="capitalize font-medium">
                            Agent Earnings:{" "}
                            {agent?.maker_total_earning
                              ? agent?.maker_total_earning
                              : 0}{" "}
                            Credits
                          </span>
                        </div>

                        <div
                          className={`flex items-center text-[10px] space-x-1 px-3 py-1 rounded-full bg-green-100`}
                        >
                          <span className="capitalize font-medium">
                            Telex Earnings:{" "}
                            {agent?.telex_total_earning
                              ? agent?.telex_total_earning
                              : 0}{" "}
                            Credits
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Agent Name</p>
                          <p className="font-medium">
                            {agent?.app_name || agent?.name}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-500">Provider</p>
                          <p className="font-medium">
                            {agent?.provider?.organization
                              ? agent?.provider?.organization
                              : "Unknown"}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-500">Type</p>
                          {agent?.is_paid ? (
                            <p className="font-medium">Paid Agent</p>
                          ) : (
                            <p className="font-medium">Free Agent</p>
                          )}
                        </div>
                        <div>
                          <p className="text-gray-500">Date Created</p>
                          <p className="font-medium">
                            {new Date(agent?.created_at).toLocaleString(
                              "en-US",
                              {
                                weekday: "long",
                                month: "long",
                                day: "2-digit",
                                year: "numeric",
                              }
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                    <br />
                  </Link>
                ))}
              </div>
            </div>

            <div className="flex items-center pt-5 mt-5 pb-10 mb-10 justify-around px-6 space-x-2 py-4">
              <Button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                variant="outline"
              >
                <ArrowLeft className="text-[#667085] w-4 h-4" /> Previous
              </Button>

              <div className="">
                {getPaginationRange(
                  currentPage,
                  Math.ceil(agents.length / 10)
                ).map((page, idx) => (
                  <button
                    key={idx}
                    className={`px-3 py-1 rounded ${
                      page === currentPage
                        ? "bg-[#E6EAEF] font-medium"
                        : "hover:bg-gray-100"
                    } ${page === "..." ? "cursor-default" : "cursor-pointer"}`}
                    onClick={() =>
                      typeof page === "number" && setCurrentPage(page)
                    }
                    disabled={page === "..."}
                  >
                    {page}
                  </button>
                ))}
              </div>

              <Button
                onClick={() => {
                  setCurrentPage((prev) =>
                    prev < Math.ceil(agents.length / 10) ? prev + 1 : prev
                  );
                }}
                disabled={currentPage === Math.ceil(agents.length / 10)}
                variant="outline"
                className="flex items-center gap-1"
              >
                Next <ArrowRight className="text-[#667085] w-4 h-4" />
              </Button>
            </div>
          </>
        ) : (
          <EmptyState
            title="There are no Agents yet"
            msg="Create, View, Manage, and Monitor All Agents in One Place"
            imgSrc="/image/empty-agent.svg"
          />
        )}
      </div>
    </>
  );
}

export default Agents;
