// ✅Correct way
"use client";
import React from "react";
import Image from "next/image";

interface EmptyStateProps {
  title: string;
  msg: string;
  imgSrc?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ title, msg, imgSrc }) => {
  return (
    <div className="w-full h-screen flex flex-col">
      <div className="w-full h-screen flex flex-col">
        <div className="flex flex-col items-center justify-center md:p-10 mt-6">
          {imgSrc && (
            <div className="relative">
              <Image
                src={imgSrc}
                height="100"
                width="100"
                alt="No app yet"
                className="w-30 md:w-[150px] h-64"
              />
            </div>
          )}

          <p className="mt-[40px] md:flex text-xl md:text-2xl text-center font-semibold leading-8">
            {title}
          </p>

          <p className="w-[300px] md:w-[481px] text-center text-sm font-light leading-6">
            {msg}
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;
