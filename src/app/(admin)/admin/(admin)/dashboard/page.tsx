"use client";
import { useState, useEffect } from "react";
import { GetRequest } from "~/utils/request";
import Link from "next/link";
import { Package, Clock, CheckCircle } from "lucide-react";
import { getStatusColor } from "~/utils/utils";

function Dashboard() {
  const [agents, setAgents] = useState<any>([]);
  const [metrics, setMetrics] = useState<any>([]);
  const [creditUsage, setCreditUsage] = useState<any>([]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const fetchAgents = async (token: string) => {
    try {
      const response = await GetRequest(`/backoffice/agents/all`, token);
      const result = response.data.data;

      setAgents(result);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchMetrics = async (token: string) => {
    try {
      const response = await GetRequest(`/backoffice/agents/metrics`, token);
      const result = response.data.data;
      setMetrics(result);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchCreditUSage = async (token: string) => {
    try {
      const response = await GetRequest(`/backoffice/credits/usage`, token);
      console.log(response);
      const result = response.data.data;
      setCreditUsage(result);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    const token: any = localStorage.getItem("admintoken");
    fetchAgents(token);
    fetchMetrics(token);
    fetchCreditUSage(token);
  }, []);

  return (
    <>
      <div className="mx-auto xl:max-w-[1440px] grow max-w-screen">
        <div className="mt-5 p-4">
          <h2 className="font-bold text-[20px]">Dashboard</h2>
          <p className="text-gray-600 text-[14px]">
            Welcome to Your Agents Dashboard — View, Manage, and Monitor All
            Agents in One Place
          </p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 p-4">
          <div className="p-5 bg-white border border-gray-100 rounded-md">
            <span className="text-2xl font-bold">{metrics?.active ?? 0}</span>
            <p className="text-gray-400 mb-5 text-[12px]">
              For {metrics?.organizations ?? 0} organizations
            </p>

            <p className="text-sm pt-5 text-gray-600 font-semibold">
              Active Agents
            </p>

            <div className="w-full mt-4 pb-2">
              <div className="bg-gray-300 h-2 rounded-full">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{
                    width: `${Math.min(((metrics?.active || 0) / 100) * 100, 100)}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>

          <div className="p-5 bg-white border border-gray-100 rounded-md">
            <span className="text-2xl font-bold">{metrics?.inactive ?? 0}</span>
            <p className="text-gray-400 mb-5 text-[12px]">
              For {metrics?.organizations ?? 0} organizations
            </p>
            <p className="text-sm pt-5 text-gray-600 font-semibold">
              Inactive Agents
            </p>

            <div className="w-full mt-4 pb-2">
              <div className="bg-gray-300 h-2 rounded-full">
                <div
                  className="bg-red-800 h-2 rounded-full"
                  style={{
                    width: `${Math.min(((metrics?.inactive || 0) / 100) * 100, 100)}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>

          <div className="p-5 bg-white border border-gray-100 rounded-md">
            <span className="text-2xl font-bold">
              {metrics?.organizations ?? 0}
            </span>
            <p className="text-gray-400 mb-5 text-[12px]">
              Including Inactive organizations
            </p>
            <p className="text-sm pt-5 text-gray-600 font-semibold">
              Organizations
            </p>

            <div className="w-full mt-4 pb-2">
              <div className="bg-gray-300 h-2 rounded-full">
                <div
                  className="bg-[#7b50fb] h-2 rounded-full"
                  style={{
                    width: `${Math.min(((metrics?.organizations || 0) / 500) * 100, 100)}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>

          <div className="p-5 bg-white border border-gray-100 rounded-md">
            <span className="text-2xl font-bold">{metrics?.credits ?? 0}</span>
            <p className="text-gray-400 mb-5 text-[12px]">
              Accumulated accross all organizations
            </p>
            <p className="text-sm pt-5 font-semibold text-gray-600">
              Credit Accumulated
            </p>

            <div className="w-full mt-4 pb-2">
              <div className="bg-gray-300 h-2 rounded-full">
                <div
                  className="bg-yellow-500 h-2 rounded-full"
                  style={{
                    width: `${Math.min(((metrics?.credits || 0) / 1000) * 100, 100)}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 pb-10">
          <div className="p-5 bg-white rounded-md border border-gray-100">
            <p className="font-bold text-[16px] border-b pb-2 border-gray-100 mb-3 text-gray-600">
              Recent Added Agents
            </p>

            <div className="space-y-4">
              {agents?.slice(0, 4).map((agent: any, idx: number) => (
                <Link
                  key={idx}
                  href={
                    agent?.source == "organization"
                      ? `/admin/agents/organization/${agent?.integration_id}`
                      : `/admin/agents/integration/${agent?.id}`
                  }
                >
                  <div className="p-6 border rounded-lg space-y-4 hover gap-2 hover:bg-gray-50 transition">
                    <div className="flex items-center justify-between pb-3">
                      <div className="flex items-center space-x-1">
                        <div className="p-2 bg-solar-green-100 rounded-full">
                          <Package className="h-5 w-5 text-solar-green-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold">
                            {agent?.app_name || agent?.name}
                          </h3>
                        </div>
                      </div>
                      <div
                        className={`flex items-center space-x-1 px-3 py-1 rounded-full border ${agent?.is_active ? getStatusColor("active") : getStatusColor("inactive")}`}
                      >
                        {agent?.is_active ? (
                          <>
                            {getStatusIcon("active")}
                            <span className="capitalize font-medium">
                              Active
                            </span>
                          </>
                        ) : (
                          <>
                            {getStatusIcon("inactive")}
                            <span className="capitalize font-medium">
                              inactive
                            </span>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Agent Name</p>
                        <p className="font-medium">
                          {agent?.app_name || agent?.name}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-500">Provider</p>
                        <p className="font-medium">
                          {agent?.provider?.organization
                            ? agent?.provider?.organization
                            : "Unknown"}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-500">Type</p>
                        <p className="font-medium">Free</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Date Created</p>
                        <p className="font-medium">
                          {new Date(agent?.created_at).toLocaleString("en-US", {
                            weekday: "long",
                            month: "long",
                            day: "2-digit",
                            year: "numeric",
                          })}
                        </p>
                      </div>
                    </div>
                  </div>
                  <br />
                </Link>
              ))}
            </div>
          </div>

          <div className="p-5 bg-gray-100 h-[600px] bg-white rounded-md border border-gray-100">
            <p className="font-bold text-[16px] border-b pb-2 border-gray-100 mb-3 text-gray-600">
              Recent Credit Charge
            </p>

            <div className="space-y-4">
              {creditUsage?.slice(0, 4).map((usage: any, idx: number) => (
                <div key={idx} className="p-6 border rounded-lg space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Organization</p>
                      <p className="font-medium">{usage?.org_name}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Agent Name</p>
                      <p className="font-medium">
                        {usage?.agent_name || "Unknown"}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">Amount</p>
                      <p className="font-medium">{usage?.amount} credits</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Date Created</p>
                      <p className="font-medium">
                        {new Date(usage?.created_at).toLocaleString("en-US", {
                          weekday: "long",
                          month: "long",
                          day: "2-digit",
                          year: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Dashboard;
