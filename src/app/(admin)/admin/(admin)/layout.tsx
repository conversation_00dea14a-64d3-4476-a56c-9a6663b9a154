"use client";
import { useRouter } from "next/navigation";
import React, { useContext, useEffect, useState } from "react";
import { ACTIONS } from "~/store/Actions";
import { DataContext, DataProvider } from "~/store/GlobalState";
import SideBar from "~/components/admin/Sidebar";
import ErrorBoundary from "~/components/error-boundary/Error-boundary";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { dispatch } = useContext(DataContext);

  useEffect(() => {
    const token = localStorage.getItem("admintoken");
    dispatch({ type: ACTIONS.TOKEN, payload: token });

    if (!token) {
      router.push("/admin/login");
      return;
    }

    setLoading(false);
  }, [dispatch, router]);

  if (loading) return;

  return (
    <DataProvider>
      <ErrorBoundary>
        <div className="w-full flex relative bg-gray-100">
          <SideBar />

          <div className={`md:ml-[250px] w-full relative`}>{children}</div>
        </div>
      </ErrorBoundary>
    </DataProvider>
  );
}
