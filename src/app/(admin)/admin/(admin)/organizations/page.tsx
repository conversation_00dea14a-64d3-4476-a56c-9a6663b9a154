"use client";
import { useState, useEffect } from "react";
import { GetRequest } from "~/utils/request";

import { But<PERSON> } from "~/components/ui/button";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { RefreshEntities } from "~/utils/utils";

import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuRadioGroup,
} from "~/components/ui/dropdown-menu";

import { Search, RefreshCw, Filter } from "lucide-react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import LoadingState from "../component/loadindState";
import EmptyState from "../component/emptyState";

function Organizations() {
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isRotating, setIsRotating] = useState(false);
  const [organizations, setOrg] = useState<any>([]);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [isOrgFound, setIsOrgFound] = useState(false);

  var totalPages = 10;
  var currentPage = 1;

  useEffect(() => {
    const fetchOrganizations = async () => {
      const token: any = localStorage.getItem("admintoken");

      try {
        const response = await GetRequest(
          `/backoffice/organisations/all`,
          token
        );
        console.log(response);
        const result = response.data.data;

        setOrg(result);
        setIsOrgFound(true);
      } catch (error) {
        console.log(error);
        setIsOrgFound(false);
      }
      setIsPageLoading(false);
    };

    fetchOrganizations();
  }, []);

  return (
    <>
      <div className="mx-auto xl:max-w-[1440px] grow max-w-screen">
        <div className="mt-5 p-4">
          <h2 className="font-bold text-[20px]">Organizations</h2>
          <p className="text-gray-600 text-[14px]">
            Welcome to Your Agents Dashboard — View, Manage, and Monitor All
            Agents in One Place
          </p>
        </div>

        <div className="flex justify-between mb-5 p-4">
          <div className="flex items-center relative w-[500px]">
            <Search className="absolute left-3 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search organizations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 pl-9 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#7b50fb]"
            />
          </div>
          <div className="flex flex-row">
            <div className="mr-5">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    {filter}
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent side="bottom" align="start">
                  <DropdownMenuLabel>Filter Agents</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={filter}
                    onValueChange={(value) => setFilter(value)}
                  >
                    <DropdownMenuRadioItem value="All">
                      All
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="Active">
                      Active
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="Inactive">
                      Inactive
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <button
              onClick={() => RefreshEntities(setIsRotating)}
              aria-label="RefreshCw"
              className="py-1 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition"
            >
              <RefreshCw
                className={`h-4 w-4 text-gray-500 transition-transform ${
                  isRotating ? "animate-spin" : ""
                }`}
              />
            </button>
          </div>
        </div>

        {isPageLoading && <LoadingState />}

        {!isPageLoading && isOrgFound ? (
          <>
            <div className="bg-white capitalize">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Organization Name</TableHead>
                    <TableHead>Credit Balance</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Country</TableHead>
                    <TableHead>Date Created</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {organizations?.map((org: any) => (
                    <TableRow
                      key={org.id}
                      className="hover:bg-gray-50 transition-colors"
                    >
                      <TableCell>{org.name}</TableCell>
                      <TableCell>{org.credit_balance} Credits</TableCell>
                      <TableCell>{org.type}</TableCell>
                      <TableCell>{org.country}</TableCell>
                      <TableCell>
                        {new Date(org.created_at).toLocaleString("en-US", {
                          weekday: "long",
                          month: "long",
                          day: "2-digit",
                          year: "numeric",
                        })}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center pt-5 mt-5 pb-10 mb-10 justify-between px-6 space-x-2 py-4">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-300"
              >
                <Icons name="move-left" svgProps={{}} />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                  let pageNum: number;
                  if (totalPages <= 7) {
                    pageNum = i + 1;
                  } else if (currentPage <= 4) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 3) {
                    pageNum = totalPages - 6 + i;
                  } else {
                    pageNum = currentPage - 3 + i;
                  }

                  if (pageNum === 4 && currentPage > 4 && totalPages > 7) {
                    return (
                      <span key="ellipsis1" className="px-2">
                        ...
                      </span>
                    );
                  }
                  if (
                    pageNum === totalPages - 3 &&
                    currentPage < totalPages - 3 &&
                    totalPages > 7
                  ) {
                    return (
                      <span key="ellipsis2" className="px-2">
                        ...
                      </span>
                    );
                  }

                  return (
                    <Button
                      key={pageNum}
                      variant={currentPage === pageNum ? "default" : "outline"}
                      size="sm"
                      className="w-8 h-8 p-0"
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 border-gray-300"
              >
                Next
                <Icons name="move-right" svgProps={{}} />
              </Button>
            </div>
          </>
        ) : (
          <EmptyState
            title="There are no organizations yet"
            msg="Create, View, Manage, and Monitor All Agents in One Place"
            imgSrc="/image/empty-agent.svg"
          />
        )}
      </div>
    </>
  );
}

export default Organizations;
