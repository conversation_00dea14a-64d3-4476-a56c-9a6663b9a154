"use client";
import { useEffect, useState } from "react";
import { Mail } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { GetRequest, InviteRequest, PostRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";
import GoogleLogin from "react-google-login";
import { useRouter, useSearchParams } from "next/navigation";

const InviteForm = () => {
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [buttonLoading, setButtonLoading] = useState(false);
  const [marketingOptIn, setMarketingOptIn] = useState(false);
  const [googleloading, setGoogleloading] = useState(false);
  const router = useRouter();
  const [orgData, setOrgData] = useState<any>(null);
  const searchParams = useSearchParams();
  const org_id = searchParams.get("org_id");
  const invitation_token = searchParams.get("invitation_token");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (org_id) {
      localStorage.setItem("orgId", org_id);

      const getOrgDetails = async () => {
        const res = await GetRequest(`/organisations/${org_id}/load-org-info`);
        if (res?.status === 200 || res?.status === 201) {
          setOrgData(res?.data?.data);
        }
        setLoading(false);
      };
      getOrgDetails();
    }
  }, [org_id]);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    setButtonLoading(true);
    const payload = {
      email,
      password,
    };

    const res = await InviteRequest("/auth/register", payload);

    if (res?.status === 200 || res?.status === 201) {
      localStorage.setItem("token", res?.data?.data?.access_token);
      localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));

      setTimeout(() => {
        handleAccept();
      }, 1500);
    } else {
      setButtonLoading(false);
    }
  };

  const onSuccess = async (response: any) => {
    setGoogleloading(true);
    if (response?.tokenId) {
      const res = await PostRequest("/auth/google", {
        id_token: response?.tokenId,
      });

      if (res?.status === 200 || res?.status === 201) {
        localStorage.setItem("token", res?.data?.data?.access_token);
        localStorage.setItem("user", JSON.stringify(res?.data?.data?.user));

        setTimeout(() => {
          handleAccept();
        }, 1500);
      } else {
        setButtonLoading(false);
      }
    }
  };

  const handleAccept = async () => {
    const reqBody = {
      token: invitation_token,
    };

    setButtonLoading(true);

    const res = await InviteRequest("/invite/general/verify", reqBody);

    if (res?.status === 200 || res?.status === 201) {
      router.push(`/client/invited?org_id=${org_id}`);
    } else {
      setButtonLoading(false);
    }
  };

  //

  return (
    <div className="w-full pb-20">
      <div className="flex flex-col items-center justify-center w-full bg-[#faf8f6] py-10 rounded-md text-center px-6">
        <Link href="/">
          <Image src="/login_logo.svg" alt="" width={86} height={31} />
        </Link>

        {loading ? (
          <div className="w-full h-[30vh] max-w-[48rem] mx-auto flex items-center justify-center py-[2rem]">
            <Loading height="50" width="50" color="#7141F8" />
          </div>
        ) : (
          <>
            <h1 className="text-3xl sm:text-4xl lg:text-[48px] font-bold leading-tight mt-14">
              See what{" "}
              <span className="text-[#6E1EFF] capitalize">
                {orgData?.organisation_name}
              </span>{" "}
              is up to
            </h1>

            <p className="mt-2 text-gray-700 text-base">
              Telex is where work happens for companies of all sizes.
            </p>

            {/* Avatars */}
            <div className="mt-6 flex items-center justify-center">
              <div className="flex -space-x-3">
                {orgData?.users_photos?.map((item: string, index: number) => {
                  return (
                    <Image
                      key={index}
                      src={item}
                      className="size-14 rounded-lg border-2 border-white object-cover"
                      alt="members"
                      width={100}
                      height={100}
                    />
                  );
                })}
              </div>
            </div>
            <p className="mt-1 text-base text-gray-600">
              {orgData?.org_user_info}.
            </p>
          </>
        )}
      </div>

      <div className="max-w-lg w-full mx-auto bg-white rounded-md text-center">
        {/* Auth Options or Form */}
        {!showEmailForm ? (
          <div className="text-left mt-10 space-y-4 px-8">
            <p className="text-base font-medium text-center">
              We suggest using the{" "}
              <strong>email account you use for work.</strong>
            </p>

            <GoogleLogin
              clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || ""}
              render={(renderProps) => (
                <div
                  onClick={renderProps.onClick}
                  className="cursor-pointer flex flex-row gap-[10px] border border-[#D0D0FD] rounded-md justify-center py-[11px]"
                >
                  <Image
                    src="/google.svg"
                    width={24}
                    height={24}
                    alt="google"
                  />
                  <div className="text-[16px] leading-[20.16px]">
                    {googleloading ? (
                      <span className="flex items-center gap-x-2">
                        <span className="animate-pulse">Logging in...</span>{" "}
                        <Loading width="20" height="20" color="#7141F8" />
                      </span>
                    ) : (
                      "Continue With Google"
                    )}
                  </div>
                </div>
              )}
              buttonText="Sign in with Google"
              className="w-full flex align-center justify-center text-md shadow-none"
              onSuccess={onSuccess}
              cookiePolicy={"single_host_origin"}
              prompt="login"
            />

            <button
              className="w-full border border-gray-300 rounded-md py-2 flex items-center justify-center gap-2 hover:bg-gray-50 transition"
              onClick={() => setShowEmailForm(true)}
            >
              <Mail size={22} />
              <span className="font-medium">Continue With Email</span>
            </button>
          </div>
        ) : (
          <form
            onSubmit={handleSubmit}
            className="text-left mt-10 space-y-5 px-6"
          >
            <p className="text-sm font-medium">
              We suggest using the{" "}
              <strong>email account you use for work.</strong>
            </p>

            <div>
              <label htmlFor="email" className="block text-sm font-medium">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full mt-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-600"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium">
                Password
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full mt-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-600"
                placeholder="********"
                required
              />
            </div>

            <button
              // type="submit"
              disabled={buttonLoading || email === "" || password === ""}
              className="w-full bg-blue-200 hover:bg-blue-400 text-white font-semibold py-3 rounded-md flex items-center justify-center transition"
            >
              Continue
              {buttonLoading && (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              )}
            </button>

            <div className="flex items-start gap-2 mt-3">
              <input
                id="marketing"
                type="checkbox"
                checked={marketingOptIn}
                onChange={(e) => setMarketingOptIn(e.target.checked)}
                className="mt-1"
              />
              <label htmlFor="marketing" className="text-sm text-gray-600">
                It’s okay to send me marketing communications about Salesforce,
                including Telex. I can unsubscribe at any time.
              </label>
            </div>

            <p className="text-xs text-gray-500 mt-3">
              By continuing, you’re agreeing to our{" "}
              <a
                href="/terms-of-service"
                target="_blank"
                className="text-blue-600 underline"
              >
                User Terms of Service
              </a>
              . Additional disclosures are available in our{" "}
              <a
                href={`/policy`}
                target="_blank"
                className="text-blue-600 underline"
              >
                Privacy Policy
              </a>
              .
            </p>
          </form>
        )}
      </div>
    </div>
  );
};

export default InviteForm;
