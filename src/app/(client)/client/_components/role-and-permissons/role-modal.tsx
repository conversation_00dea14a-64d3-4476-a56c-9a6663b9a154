"use client";
import React, { useContext, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogOverlay,
  DialogTitle,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { CheckIcon, X } from "lucide-react";
import cogoToast from "cogo-toast";
import { PostRequest, PutRequest } from "~/utils/new-request";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";

interface StorageOffloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  isNew?: boolean;
  roleId?: string;
  roleName?: string;
  description?: string;
  permissions?: any;
  // eslint-disable-next-line
  onRoleCreated?: (_role: any) => void;
}

interface Permission {
  id: string;
  name: string;
  enabled: boolean;
}

const RoleModal: React.FC<StorageOffloadModalProps> = ({
  isOpen,
  onClose,
  isNew = false,
  roleId,
  roleName: initialRoleName,
  description: initialDescription,
  permissions: initialPermissions,
  onRoleCreated,
}) => {
  const [roleName, setRoleName] = useState<string>(initialRoleName || "");
  const [roleDescription, setRoleDescription] = useState<string | null>(
    initialDescription || null
  );
  const [permissions, setPermissions] = useState<Permission[]>([
    { id: "can_view_billing", name: "View billing settings", enabled: false },
    { id: "can_invite_members", name: "Invite members", enabled: false },
    {
      id: "can_remove_people_from_organization",
      name: "Remove members",
      enabled: false,
    },
    { id: "can_create_webhooks", name: "Create webhooks", enabled: false },
    { id: "can_create_channel", name: "Create channels", enabled: false },
    { id: "can_view_channels", name: "View channels", enabled: true },
    {
      id: "can_comment_on_threads",
      name: "Comment on threads",
      enabled: false,
    },
    {
      id: "can_create_custom_role",
      name: "Create custom roles",
      enabled: false,
    },
    {
      id: "can_change_user_org_role",
      name: "Change user organization role",
      enabled: false,
    },
  ]);
  const [buttonLoading, setButtonLoading] = useState(false);
  const { state, dispatch } = useContext(DataContext);

  useEffect(() => {
    if (!isNew && initialPermissions) {
      setPermissions((prev) =>
        prev.map((permission) => ({
          ...permission,
          enabled:
            Object.keys(initialPermissions).includes(permission.id) &&
            initialPermissions[permission.id],
        }))
      );
    }
  }, [isNew, initialPermissions]);

  const togglePermission = (permissionId: string) => {
    setPermissions((prev) =>
      prev.map((permission) =>
        permission.id === permissionId
          ? { ...permission, enabled: !permission.enabled }
          : permission
      )
    );
  };

  const handleSaveChanges = async () => {
    setButtonLoading(true);

    try {
      if (isNew) {
        onClose();
        cogoToast.success("Role created successfully");

        // Make the actual API call
        const response = await PostRequest(
          `/organisations/${state.orgId}/roles`,
          {
            name: roleName,
            description: roleDescription || "",
            // permissions: enabledPermissions,
          }
        );

        if (response?.status === 200 || response?.status === 201) {
          const newRole = response.data?.data || response.data;
          if (onRoleCreated) {
            onRoleCreated(newRole);
          }
        } else {
          throw new Error("Failed to create role");
        }
      } else {
        const response = await PutRequest(
          `/organisations/${state.orgId}/roles/${roleId}`,
          {
            name: roleName,
            description: roleDescription || "",
            // permissions: enabledPermissions,
          }
        );
        console.log(response);
        cogoToast.success("Role updated successfully");
        dispatch({
          type: ACTIONS.ORG_ROLES,
          payload: state?.orgRoles,
        });
        onClose();
      }
    } catch (error) {
      if (isNew) {
        const currentRoles = state?.orgRoles || [];
        const rolledBackRoles = currentRoles.filter(
          (role: any) => !role.id.startsWith("temp-")
        );

        dispatch({
          type: ACTIONS.ORG_ROLES,
          payload: rolledBackRoles,
        });
        cogoToast.error("Failed to create role");
      } else {
        cogoToast.error("Failed to update role");
      }
    } finally {
      setButtonLoading(false);
    }
  };

  const getCharacterCount = () => {
    return `${roleDescription ? roleDescription.length : 0}/36`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="backdrop-blur-sm bg-black/50">
        <DialogContent className="sm:max-w-2xl bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden max-h-[90vh] overflow-y-auto">
          <div className="relative">
            <DialogHeader className="px-6 pt-6 pb-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <DialogTitle className="text-xl font-semibold text-gray-900">
                  {isNew ? "Create New Role" : "Update Role"}
                </DialogTitle>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="p-1 rounded-md hover:bg-gray-100"
                >
                  <X size={20} className="text-gray-500" />
                </Button>
              </div>
            </DialogHeader>

            <div className="px-6 py-6 space-y-6">
              {/* Role Name */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-900">
                  Role Name
                </label>
                <input
                  type="text"
                  value={roleName}
                  onChange={(e) => setRoleName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g. Manager"
                />
              </div>

              {/* Role Description */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-900">
                  Role Description
                </label>
                <div className="relative">
                  <textarea
                    value={roleDescription || ""}
                    onChange={(e) => setRoleDescription(e.target.value)}
                    maxLength={36}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    rows={3}
                    placeholder="e.g. Tier 2 - Second- in-command"
                  />
                  <div className="absolute bottom-2 right-2 text-xs text-gray-400">
                    {getCharacterCount()}
                  </div>
                </div>
              </div>

              {/* Permissions */}
              <div className="space-y-4">
                {permissions.map((permission) => (
                  <div
                    key={permission.id}
                    className="flex items-center space-x-3"
                  >
                    <div className="relative">
                      <input
                        type="checkbox"
                        id={permission.id}
                        checked={permission.enabled}
                        onChange={() => togglePermission(permission.id)}
                        className="sr-only"
                      />
                      <label
                        htmlFor={permission.id}
                        className={`flex items-center justify-center w-5 h-5 border rounded cursor-pointer ${
                          permission.enabled
                            ? "bg-white border-blue-600"
                            : "bg-white border-gray-300"
                        }`}
                      >
                        {permission.enabled && (
                          <CheckIcon className="h-3 w-3 text-[#7141F8]" />
                        )}
                      </label>
                    </div>
                    <label
                      htmlFor={permission.id}
                      className="text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      {permission.name}
                    </label>
                  </div>
                ))}
              </div>

              {/* Note */}
            </div>

            {/* Footer */}
            <div className="border-t border-gray-200 my-4" />
            <div className="text-sm text-gray-500 px-5">
              <span className="font-medium">Note:</span> This affects your
              team's access.
            </div>
            <div className="px-6 py-4 bg-white flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveChanges}
                disabled={buttonLoading}
                className="bg-[#7141F8] text-white rounded-md hover:bg-[#7141F8]/80 transition-colors"
              >
                {buttonLoading ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  );
};

export default RoleModal;
