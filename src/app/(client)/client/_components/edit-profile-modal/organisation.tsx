"use client";
import React, { useState, useRef, useContext, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Upload } from "lucide-react";
import Image from "next/image";
import { DataContext } from "~/store/GlobalState";
import images from "~/assets/images";
import cogoToast from "cogo-toast";
import Loading from "~/components/ui/loading";
import { ACTIONS } from "~/store/Actions";
import { Country } from "country-state-city";
import UpdateCustomSelect from "~/components/ui/update-custom-select";
import { PutRequest } from "~/utils/new-request";
import { getInitials } from "~/utils/utils";

const EditOrganisationDialog = ({ isOpen, onClose }: any) => {
  const { state, dispatch } = useContext(DataContext);
  const { orgData: user } = state;
  const [name, setName] = useState(user?.name);
  const [type, setType] = useState(user?.type || "");
  const [avatar, setAvatar] = useState(user?.logo_url || images?.user);
  const [image, setImage] = useState("");
  const [buttonLoading, setButtonLoading] = useState(false);
  const [country, setCountry] = useState<any>(null);
  const [countries, setCountries] = useState<any>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setName(user?.name);
    setType(user?.type);
    setAvatar(user?.logo_url);

    if (user?.country && countries.length > 0) {
      const matchedCountry = countries.find(
        (c: any) => c.value.toLowerCase() === user.country.toLowerCase()
      );
      setCountry(matchedCountry || null);
    }
  }, [user, countries]);

  useEffect(() => {
    const allcountries = Country.getAllCountries();
    const response = allcountries?.map((item) => ({
      label: item.name,
      value: item.name,
    }));

    setCountries(response);
  }, []);

  const handleSubmit = async (event: React.FormEvent) => {
    const orgId = localStorage.getItem("orgId");
    event.preventDefault();

    setButtonLoading(true);

    let payload = {
      name,
      description: "company description",
      email: user?.email,
      type,
      location: "location",
      country: country?.value,
      logo_url: typeof avatar === "string" ? image : "",
    };

    const res = await PutRequest(`/organisations/${orgId}`, payload);
    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
      cogoToast.success(res?.data.message);
    }
    setButtonLoading(false);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file?.type !== "image/jpeg" && file?.type !== "image/png") {
      return cogoToast.error("Image type is not supported");
    }

    if (file) {
      const url = URL.createObjectURL(file);
      setAvatar(url);

      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemovePhoto = async () => {
    setAvatar("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  //

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] p-0 overflow-hidden max-h-[90vh]">
        <DialogHeader className={`p-6 pb-0`}>
          <DialogTitle className="text-[#1D2939] text-xl font-black">
            Update your details
          </DialogTitle>
        </DialogHeader>

        <div
          ref={contentRef}
          className={`max-h-[calc(90vh-180px)] border-t border-[#E6EAEF] space-y-5 pb-6 overflow-y-auto scrollbar-hide`}
        >
          <div className="flex">
            {/* Left side - Form fields */}
            <div className="flex-1 py-3 px-6 space-y-5">
              {/* Full Name */}
              <div className="space-y-2">
                <label
                  htmlFor="name"
                  className="text-sm text-[#101828] font-bold"
                >
                  Organisation Name
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
                />
              </div>

              {/* Title/Role */}
              <div className="space-y-2">
                <label
                  htmlFor="title"
                  className="text-sm text-[#101828] font-bold"
                >
                  Nature of Business
                </label>
                <input
                  id="title"
                  type="text"
                  value={type}
                  onChange={(e) => setType(e.target.value)}
                  className="w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="title"
                  className="text-sm text-[#101828] font-bold"
                >
                  Country
                </label>

                <UpdateCustomSelect
                  options={countries}
                  placeholder="Select an option..."
                  onChange={setCountry}
                  defaultValue={country}
                  isDisabled={false}
                />
              </div>
            </div>

            {/* Right side - Profile Picture */}
            <div className="w-[230px] pl-6 pr-3 py-3 space-y-6">
              <div className="space-y-2">
                <h3 className="text-sm text-[#101828] font-bold">
                  Organisation Logo
                </h3>
                <div className="flex flex-col items-start gap-4">
                  {avatar ? (
                    <div className="relative w-[192px] h-[192px] border rounded-[9px]">
                      <Image
                        src={avatar}
                        alt={name}
                        width={192}
                        height={192}
                        className="rounded-[9px] object-cover w-full h-full"
                      />
                    </div>
                  ) : (
                    <div className="w-[192px] h-[192px] border object-cover rounded-xl flex items-center justify-center text-3xl text-blue-200 font-bold">
                      {getInitials(name)}
                    </div>
                  )}
                  <div className="space-y-1 w-full">
                    <Button
                      variant="outline"
                      onClick={triggerFileInput}
                      className="text-[13px] h-8 text-[#344054] border-[#E6EAEF] gap-2 w-full flex items-center justify-center"
                    >
                      <Upload size={14} />
                      <span>Upload photo</span>
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleFileUpload}
                    />
                    <Button
                      onClick={handleRemovePhoto}
                      className="text-[13px] h-8 text-[#6868F7] gap-2 py-0 w-full"
                    >
                      Remove photo
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 px-6 py-5 border-t border-[#E6EAEF] sticky bottom-0 bg-white">
          <Button
            variant="outline"
            onClick={onClose}
            className="text-sm text-[#344054] h-9 border-[#E6EAEF]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="d-flex items-center gap-2 bg-[#6868F7] text-white h-9 text-sm hover:bg-[#5151d3]"
            disabled={buttonLoading}
          >
            Save Changes {buttonLoading && <Loading />}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditOrganisationDialog;
