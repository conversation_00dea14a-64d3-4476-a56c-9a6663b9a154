import React, { JSX } from "react";

const FeatureBullets = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgprops}
    >
      <path
        d="M13.4291 11.2575C13.3953 11.314 13.3506 11.3632 13.2978 11.4024C13.2449 11.4416 13.1848 11.47 13.1209 11.4859C13.057 11.5018 12.9906 11.505 12.9255 11.4952C12.8604 11.4853 12.7979 11.4628 12.7416 11.4288L8.50032 8.88313V13.5C8.50032 13.6326 8.44764 13.7598 8.35388 13.8536C8.26011 13.9473 8.13293 14 8.00032 14C7.86771 14 7.74054 13.9473 7.64677 13.8536C7.553 13.7598 7.50032 13.6326 7.50032 13.5V8.88313L3.25782 11.4288C3.20149 11.4634 3.13887 11.4864 3.07356 11.4967C3.00825 11.5069 2.94156 11.5041 2.87734 11.4884C2.81313 11.4727 2.75266 11.4444 2.69945 11.4052C2.64623 11.366 2.60133 11.3166 2.56732 11.2599C2.53331 11.2032 2.51089 11.1404 2.50134 11.075C2.49179 11.0095 2.49531 10.9429 2.51168 10.8788C2.52806 10.8148 2.55698 10.7546 2.59676 10.7018C2.63654 10.649 2.6864 10.6047 2.74345 10.5712L7.02845 8L2.74345 5.42875C2.6864 5.39535 2.63654 5.35096 2.59676 5.29817C2.55698 5.24537 2.52806 5.18521 2.51168 5.12116C2.49531 5.05712 2.49179 4.99046 2.50134 4.92505C2.51089 4.85963 2.53331 4.79676 2.56732 4.74007C2.60133 4.68339 2.64623 4.634 2.69945 4.59478C2.75266 4.55556 2.81313 4.52728 2.87734 4.51158C2.94156 4.49588 3.00825 4.49307 3.07356 4.50331C3.13887 4.51356 3.20149 4.53665 3.25782 4.57125L7.50032 7.11688V2.5C7.50032 2.36739 7.553 2.24021 7.64677 2.14645C7.74054 2.05268 7.86771 2 8.00032 2C8.13293 2 8.26011 2.05268 8.35388 2.14645C8.44764 2.24021 8.50032 2.36739 8.50032 2.5V7.11688L12.7428 4.57125C12.7991 4.53665 12.8618 4.51356 12.9271 4.50331C12.9924 4.49307 13.0591 4.49588 13.1233 4.51158C13.1875 4.52728 13.248 4.55556 13.3012 4.59478C13.3544 4.634 13.3993 4.68339 13.4333 4.74007C13.4673 4.79676 13.4898 4.85963 13.4993 4.92505C13.5089 4.99046 13.5053 5.05712 13.489 5.12116C13.4726 5.18521 13.4437 5.24537 13.4039 5.29817C13.3641 5.35096 13.3142 5.39535 13.2572 5.42875L8.9722 8L13.2572 10.5712C13.3136 10.605 13.3627 10.6495 13.4019 10.7023C13.441 10.7551 13.4694 10.815 13.4854 10.8788C13.5013 10.9425 13.5046 11.0087 13.4949 11.0737C13.4852 11.1387 13.4629 11.2012 13.4291 11.2575Z"
        fill="#00CC5F"
      />
    </svg>
  );
};

const Money = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgprops}
    >
      <path
        d="M8 5.5C7.50555 5.5 7.0222 5.64662 6.61107 5.92133C6.19995 6.19603 5.87952 6.58648 5.6903 7.04329C5.50108 7.50011 5.45157 8.00277 5.54804 8.48773C5.6445 8.97268 5.8826 9.41814 6.23223 9.76777C6.58186 10.1174 7.02732 10.3555 7.51227 10.452C7.99723 10.5484 8.49989 10.4989 8.95671 10.3097C9.41352 10.1205 9.80397 9.80005 10.0787 9.38893C10.3534 8.9778 10.5 8.49445 10.5 8C10.5 7.33696 10.2366 6.70107 9.76777 6.23223C9.29893 5.76339 8.66304 5.5 8 5.5ZM8 9.5C7.70333 9.5 7.41332 9.41203 7.16664 9.2472C6.91997 9.08238 6.72771 8.84811 6.61418 8.57403C6.50065 8.29994 6.47094 7.99834 6.52882 7.70736C6.5867 7.41639 6.72956 7.14912 6.93934 6.93934C7.14912 6.72956 7.41639 6.5867 7.70736 6.52882C7.99834 6.47094 8.29994 6.50065 8.57403 6.61418C8.84811 6.72771 9.08238 6.91997 9.2472 7.16664C9.41203 7.41332 9.5 7.70333 9.5 8C9.5 8.39782 9.34196 8.77936 9.06066 9.06066C8.77936 9.34196 8.39782 9.5 8 9.5ZM15 3.5H1C0.867392 3.5 0.740215 3.55268 0.646447 3.64645C0.552678 3.74021 0.5 3.86739 0.5 4V12C0.5 12.1326 0.552678 12.2598 0.646447 12.3536C0.740215 12.4473 0.867392 12.5 1 12.5H15C15.1326 12.5 15.2598 12.4473 15.3536 12.3536C15.4473 12.2598 15.5 12.1326 15.5 12V4C15.5 3.86739 15.4473 3.74021 15.3536 3.64645C15.2598 3.55268 15.1326 3.5 15 3.5ZM12.1031 11.5H3.89687C3.729 10.9323 3.42175 10.4155 3.00311 9.99689C2.58447 9.57825 2.06775 9.271 1.5 9.10312V6.89687C2.06775 6.729 2.58447 6.42175 3.00311 6.00311C3.42175 5.58447 3.729 5.06775 3.89687 4.5H12.1031C12.271 5.06775 12.5782 5.58447 12.9969 6.00311C13.4155 6.42175 13.9323 6.729 14.5 6.89687V9.10312C13.9323 9.271 13.4155 9.57825 12.9969 9.99689C12.5782 10.4155 12.271 10.9323 12.1031 11.5ZM14.5 5.83563C13.9003 5.57775 13.4223 5.09973 13.1644 4.5H14.5V5.83563ZM2.83562 4.5C2.57774 5.09973 2.09973 5.57775 1.5 5.83563V4.5H2.83562ZM1.5 10.1644C2.09973 10.4223 2.57774 10.9003 2.83562 11.5H1.5V10.1644ZM13.1644 11.5C13.4223 10.9003 13.9003 10.4223 14.5 10.1644V11.5H13.1644Z"
        fill="#667085"
      />
    </svg>
  );
};

const Filter = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      {...svgprops}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.125 3.87502H3.70312C3.84081 4.41278 4.15356 4.88942 4.59207 5.22979C5.03057 5.57017 5.56989 5.75492 6.125 5.75492C6.68011 5.75492 7.21943 5.57017 7.65793 5.22979C8.09644 4.88942 8.40919 4.41278 8.54688 3.87502H14.875C15.0408 3.87502 15.1997 3.80917 15.3169 3.69196C15.4342 3.57475 15.5 3.41578 15.5 3.25002C15.5 3.08426 15.4342 2.92529 15.3169 2.80808C15.1997 2.69087 15.0408 2.62502 14.875 2.62502H8.54688C8.40919 2.08726 8.09644 1.61062 7.65793 1.27024C7.21943 0.929869 6.68011 0.745117 6.125 0.745117C5.56989 0.745117 5.03057 0.929869 4.59207 1.27024C4.15356 1.61062 3.84081 2.08726 3.70312 2.62502H1.125C0.95924 2.62502 0.800269 2.69087 0.683058 2.80808C0.565848 2.92529 0.5 3.08426 0.5 3.25002C0.5 3.41578 0.565848 3.57475 0.683058 3.69196C0.800269 3.80917 0.95924 3.87502 1.125 3.87502ZM6.125 2.00002C6.37223 2.00002 6.6139 2.07333 6.81946 2.21068C7.02502 2.34803 7.18524 2.54326 7.27985 2.77167C7.37446 3.00007 7.39921 3.25141 7.35098 3.49388C7.30275 3.73636 7.1837 3.95909 7.00888 4.1339C6.83407 4.30872 6.61134 4.42777 6.36886 4.476C6.12639 4.52423 5.87505 4.49948 5.64665 4.40487C5.41824 4.31026 5.22301 4.15004 5.08566 3.94448C4.94831 3.73892 4.875 3.49725 4.875 3.25002C4.875 2.9185 5.0067 2.60056 5.24112 2.36614C5.47554 2.13172 5.79348 2.00002 6.125 2.00002ZM14.875 10.125H13.5469C13.4092 9.58726 13.0964 9.11062 12.6579 8.77024C12.2194 8.42987 11.6801 8.24512 11.125 8.24512C10.5699 8.24512 10.0306 8.42987 9.59207 8.77024C9.15356 9.11062 8.84081 9.58726 8.70312 10.125H1.125C0.95924 10.125 0.800269 10.1909 0.683058 10.3081C0.565848 10.4253 0.5 10.5843 0.5 10.75C0.5 10.9158 0.565848 11.0748 0.683058 11.192C0.800269 11.3092 0.95924 11.375 1.125 11.375H8.70312C8.84081 11.9128 9.15356 12.3894 9.59207 12.7298C10.0306 13.0702 10.5699 13.2549 11.125 13.2549C11.6801 13.2549 12.2194 13.0702 12.6579 12.7298C13.0964 12.3894 13.4092 11.9128 13.5469 11.375H14.875C15.0408 11.375 15.1997 11.3092 15.3169 11.192C15.4342 11.0748 15.5 10.9158 15.5 10.75C15.5 10.5843 15.4342 10.4253 15.3169 10.3081C15.1997 10.1909 15.0408 10.125 14.875 10.125ZM11.125 12C10.8778 12 10.6361 11.9267 10.4305 11.7894C10.225 11.652 10.0648 11.4568 9.97015 11.2284C9.87554 11 9.85079 10.7486 9.89902 10.5062C9.94725 10.2637 10.0663 10.041 10.2411 9.86614C10.4159 9.69132 10.6387 9.57227 10.8811 9.52404C11.1236 9.47581 11.3749 9.50056 11.6034 9.59517C11.8318 9.68978 12.027 9.84999 12.1643 10.0556C12.3017 10.2611 12.375 10.5028 12.375 10.75C12.375 11.0815 12.2433 11.3995 12.0089 11.6339C11.7745 11.8683 11.4565 12 11.125 12Z"
        fill="#667085"
      />
    </svg>
  );
};

const Export = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      {...svgprops}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.5 11.875V16.25C17.5 16.5815 17.3683 16.8995 17.1339 17.1339C16.8995 17.3683 16.5815 17.5 16.25 17.5H3.75C3.41848 17.5 3.10054 17.3683 2.86612 17.1339C2.6317 16.8995 2.5 16.5815 2.5 16.25V11.875C2.5 11.7092 2.56585 11.5503 2.68306 11.4331C2.80027 11.3158 2.95924 11.25 3.125 11.25C3.29076 11.25 3.44973 11.3158 3.56694 11.4331C3.68415 11.5503 3.75 11.7092 3.75 11.875V16.25H16.25V11.875C16.25 11.7092 16.3158 11.5503 16.4331 11.4331C16.5503 11.3158 16.7092 11.25 16.875 11.25C17.0408 11.25 17.1997 11.3158 17.3169 11.4331C17.4342 11.5503 17.5 11.7092 17.5 11.875ZM9.55781 12.3172C9.61586 12.3753 9.68479 12.4214 9.76066 12.4529C9.83654 12.4843 9.91787 12.5005 10 12.5005C10.0821 12.5005 10.1635 12.4843 10.2393 12.4529C10.3152 12.4214 10.3841 12.3753 10.4422 12.3172L13.5672 9.19219C13.6253 9.13412 13.6713 9.06518 13.7027 8.98931C13.7342 8.91344 13.7503 8.83212 13.7503 8.75C13.7503 8.66788 13.7342 8.58656 13.7027 8.51069C13.6713 8.43482 13.6253 8.36588 13.5672 8.30781C13.5091 8.24974 13.4402 8.20368 13.3643 8.17225C13.2884 8.14083 13.2071 8.12465 13.125 8.12465C13.0429 8.12465 12.9616 8.14083 12.8857 8.17225C12.8098 8.20368 12.7409 8.24974 12.6828 8.30781L10.625 10.3664V3.125C10.625 2.95924 10.5592 2.80027 10.4419 2.68306C10.3247 2.56585 10.1658 2.5 10 2.5C9.83424 2.5 9.67527 2.56585 9.55806 2.68306C9.44085 2.80027 9.375 2.95924 9.375 3.125V10.3664L7.31719 8.30781C7.19991 8.19054 7.04085 8.12465 6.875 8.12465C6.70915 8.12465 6.55009 8.19054 6.43281 8.30781C6.31554 8.42509 6.24965 8.58415 6.24965 8.75C6.24965 8.91585 6.31554 9.07491 6.43281 9.19219L9.55781 12.3172Z"
        fill="#667085"
      />
    </svg>
  );
};

const MoveDown = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      {...svgprops}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.8535 9.35375L8.35354 13.8538C8.3071 13.9002 8.25196 13.9371 8.19126 13.9623C8.13056 13.9874 8.0655 14.0004 7.99979 14.0004C7.93408 14.0004 7.86902 13.9874 7.80832 13.9623C7.74762 13.9371 7.69248 13.9002 7.64604 13.8538L3.14604 9.35375C3.05222 9.25993 2.99951 9.13268 2.99951 9C2.99951 8.86732 3.05222 8.74007 3.14604 8.64625C3.23986 8.55243 3.36711 8.49972 3.49979 8.49972C3.63247 8.49972 3.75972 8.55243 3.85354 8.64625L7.49979 12.2931V2.5C7.49979 2.36739 7.55247 2.24021 7.64624 2.14645C7.74 2.05268 7.86718 2 7.99979 2C8.1324 2 8.25958 2.05268 8.35334 2.14645C8.44711 2.24021 8.49979 2.36739 8.49979 2.5V12.2931L12.146 8.64625C12.2399 8.55243 12.3671 8.49972 12.4998 8.49972C12.6325 8.49972 12.7597 8.55243 12.8535 8.64625C12.9474 8.74007 13.0001 8.86732 13.0001 9C13.0001 9.13268 12.9474 9.25993 12.8535 9.35375Z"
        fill="#98A2B3"
      />
    </svg>
  );
};

const MoveRight = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      {...svgprops}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.3172 7.44205L9.69219 13.067C9.57491 13.1843 9.41585 13.2502 9.25 13.2502C9.08415 13.2502 8.92509 13.1843 8.80781 13.067C8.69054 12.9498 8.62465 12.7907 8.62465 12.6249C8.62465 12.459 8.69054 12.2999 8.80781 12.1827L13.3664 7.62486H1.125C0.95924 7.62486 0.800269 7.55901 0.683058 7.4418C0.565848 7.32459 0.5 7.16562 0.5 6.99986C0.5 6.8341 0.565848 6.67513 0.683058 6.55792C0.800269 6.44071 0.95924 6.37486 1.125 6.37486H13.3664L8.80781 1.81705C8.69054 1.69977 8.62465 1.54071 8.62465 1.37486C8.62465 1.20901 8.69054 1.04995 8.80781 0.932672C8.92509 0.815396 9.08415 0.749512 9.25 0.749512C9.41585 0.749512 9.57491 0.815396 9.69219 0.932672L15.3172 6.55767C15.3753 6.61572 15.4214 6.68465 15.4529 6.76052C15.4843 6.8364 15.5005 6.91772 15.5005 6.99986C15.5005 7.08199 15.4843 7.16332 15.4529 7.2392C15.4214 7.31507 15.3753 7.384 15.3172 7.44205Z"
        fill="#667085"
      />
    </svg>
  );
};

const MoveLeft = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      {...svgprops}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.5 6.99986C15.5 7.16562 15.4342 7.32459 15.3169 7.4418C15.1997 7.55901 15.0408 7.62486 14.875 7.62486H2.6336L7.19219 12.1827C7.25026 12.2407 7.29632 12.3097 7.32775 12.3855C7.35918 12.4614 7.37535 12.5427 7.37535 12.6249C7.37535 12.707 7.35918 12.7883 7.32775 12.8642C7.29632 12.94 7.25026 13.009 7.19219 13.067C7.13412 13.1251 7.06519 13.1712 6.98931 13.2026C6.91344 13.234 6.83213 13.2502 6.75 13.2502C6.66788 13.2502 6.58656 13.234 6.51069 13.2026C6.43482 13.1712 6.36589 13.1251 6.30782 13.067L0.682816 7.44205C0.624706 7.384 0.578606 7.31507 0.547154 7.2392C0.515701 7.16332 0.499512 7.08199 0.499512 6.99986C0.499512 6.91772 0.515701 6.8364 0.547154 6.76052C0.578606 6.68465 0.624706 6.61572 0.682816 6.55767L6.30782 0.932672C6.42509 0.815396 6.58415 0.749512 6.75 0.749512C6.91586 0.749512 7.07492 0.815396 7.19219 0.932672C7.30947 1.04995 7.37535 1.20901 7.37535 1.37486C7.37535 1.54071 7.30947 1.69977 7.19219 1.81705L2.6336 6.37486H14.875C15.0408 6.37486 15.1997 6.44071 15.3169 6.55792C15.4342 6.67513 15.5 6.8341 15.5 6.99986Z"
        fill="#667085"
      />
    </svg>
  );
};

const BillingLabel = ({ ...svgprops }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="87"
      height="36"
      viewBox="0 0 87 36"
      fill="none"
      {...svgprops}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M39.2899 9.166C39.2899 9.754 39.0286 10.2347 38.5059 10.608C37.9926 10.9813 37.4046 11.168 36.7419 11.168C36.0793 11.168 35.5519 11.028 35.1599 10.748C34.7773 10.468 34.5859 10.2207 34.5859 10.006C34.5859 9.87533 34.7493 9.66533 35.0759 9.376C35.4026 9.07733 35.6779 8.89533 35.9019 8.83C36.3686 9.17533 36.7513 9.72133 37.0499 10.468C37.3953 10.44 37.5679 10.2767 37.5679 9.978C37.5679 9.54867 37.1899 8.942 36.4339 8.158C35.6779 7.36467 35.2999 6.73467 35.2999 6.268C35.2999 5.80133 35.5099 5.442 35.9299 5.19C36.3499 4.92867 36.8539 4.798 37.4419 4.798C38.0393 4.798 38.4873 4.90533 38.7859 5.12C39.0846 5.32533 39.2339 5.61 39.2339 5.974C39.2339 6.32867 38.9493 6.82333 38.3799 7.458C38.4453 7.52333 38.5293 7.612 38.6319 7.724C38.7346 7.82667 38.8699 8.02733 39.0379 8.326C39.2059 8.62467 39.2899 8.90467 39.2899 9.166ZM37.9459 6.968C38.3379 6.632 38.5339 6.30067 38.5339 5.974C38.5339 5.64733 38.3099 5.484 37.8619 5.484C37.6473 5.484 37.4699 5.53067 37.3299 5.624C37.1899 5.708 37.1199 5.806 37.1199 5.918C37.1199 6.12333 37.3253 6.41267 37.7359 6.786L37.9459 6.968ZM44.1125 5.204C44.1312 5.18533 44.1825 5.064 44.2665 4.84C44.7052 4.84 45.2932 4.97067 46.0305 5.232C45.7972 6.00667 45.6105 6.81867 45.4705 7.668C45.3305 8.508 45.2605 9.11 45.2605 9.474C45.2605 9.82867 45.2978 10.006 45.3725 10.006C45.4285 10.006 45.6338 9.91733 45.9885 9.74L46.1565 9.656L46.4085 10.146C46.3245 10.2207 46.2125 10.314 46.0725 10.426C45.9418 10.538 45.6898 10.692 45.3165 10.888C44.9432 11.0747 44.6072 11.168 44.3085 11.168C43.7298 11.168 43.3938 10.9067 43.3005 10.384C42.7032 10.9067 42.1618 11.168 41.6765 11.168C41.2005 11.168 40.7945 10.9813 40.4585 10.608C40.1318 10.2347 39.9685 9.64667 39.9685 8.844C39.9685 7.59333 40.2252 6.60867 40.7385 5.89C41.2518 5.162 41.8725 4.798 42.6005 4.798C43.1232 4.798 43.6272 4.93333 44.1125 5.204ZM42.5165 9.978C42.7405 9.978 42.9925 9.88933 43.2725 9.712C43.3378 8.396 43.5572 7.094 43.9305 5.806C43.6505 5.68467 43.4125 5.624 43.2165 5.624C42.8805 5.624 42.5912 5.95533 42.3485 6.618C42.1152 7.27133 41.9985 7.99467 41.9985 8.788C41.9985 9.58133 42.1712 9.978 42.5165 9.978ZM48.0251 4.798C48.4357 4.798 48.7391 4.882 48.9351 5.05C49.1404 5.218 49.2431 5.56333 49.2431 6.086C49.2431 6.366 49.1591 6.94 48.9911 7.808C48.8231 8.66667 48.7391 9.18933 48.7391 9.376C48.7391 9.74933 48.8137 9.936 48.9631 9.936C49.3177 9.936 49.7051 9.67467 50.1251 9.152C50.5544 8.62 50.7784 8.17667 50.7971 7.822L49.8451 6.758C49.8824 6.35667 49.9804 5.99267 50.1391 5.666C50.2977 5.33933 50.4471 5.11067 50.5871 4.98L50.7831 4.798C51.1844 4.798 51.4877 4.90533 51.6931 5.12C51.8984 5.33467 52.0011 5.58667 52.0011 5.876C52.0011 6.61333 51.8191 7.39267 51.4551 8.214C51.0911 9.03533 50.6057 9.73533 49.9991 10.314C49.3924 10.8833 48.7764 11.168 48.1511 11.168C47.7591 11.168 47.4557 11.0467 47.2411 10.804C47.0357 10.5613 46.9331 10.244 46.9331 9.852C46.9331 9.45067 46.9611 8.914 47.0171 8.242C47.0824 7.57 47.1151 6.90267 47.1151 6.24C47.1151 6.09067 47.0591 5.91333 46.9471 5.708C46.8444 5.50267 46.7371 5.33933 46.6251 5.218L46.6671 5.05C47.1711 4.882 47.6237 4.798 48.0251 4.798ZM52.1208 8.578C52.1208 7.41133 52.4708 6.492 53.1708 5.82C53.8802 5.13867 54.6828 4.798 55.5788 4.798C56.1295 4.798 56.5822 4.93333 56.9368 5.204C57.2915 5.47467 57.4688 5.83867 57.4688 6.296C57.4688 6.744 57.3522 7.122 57.1188 7.43C56.8948 7.738 56.6195 7.976 56.2928 8.144C55.6302 8.47067 55.0235 8.676 54.4728 8.76L54.1368 8.802C54.2022 9.67933 54.5615 10.118 55.2148 10.118C55.4388 10.118 55.6768 10.062 55.9288 9.95C56.1808 9.838 56.3768 9.726 56.5168 9.614L56.7268 9.446L57.0628 9.894C56.9882 9.99667 56.8388 10.132 56.6148 10.3C56.3908 10.468 56.1808 10.608 55.9848 10.72C55.4435 11.0187 54.8508 11.168 54.2068 11.168C53.5628 11.168 53.0542 10.9393 52.6808 10.482C52.3075 10.0247 52.1208 9.39 52.1208 8.578ZM54.1228 8.116C54.5988 8.032 54.9768 7.83133 55.2568 7.514C55.5368 7.19667 55.6768 6.786 55.6768 6.282C55.6768 5.778 55.5275 5.526 55.2288 5.526C54.8742 5.526 54.5988 5.82933 54.4028 6.436C54.2162 7.03333 54.1228 7.59333 54.1228 8.116ZM65.3594 11.252C65.0327 11.252 64.5427 11.0653 63.8894 10.692C63.236 10.3093 62.6947 10.0573 62.2654 9.936C61.948 10.272 61.64 10.5473 61.3414 10.762C61.052 10.9673 60.856 11.07 60.7534 11.07C60.6507 11.07 60.464 10.9067 60.1934 10.58C59.932 10.244 59.8014 9.992 59.8014 9.824C59.8014 9.64667 59.9554 9.39933 60.2634 9.082C60.5807 8.75533 60.9447 8.592 61.3554 8.592C61.6074 8.592 61.8547 8.62 62.0974 8.676C62.7507 8.15333 63.3714 7.39733 63.9594 6.408C64.5474 5.40933 64.8414 4.53667 64.8414 3.79C64.8414 3.37 64.7154 3.04333 64.4634 2.81C64.2114 2.56733 63.9034 2.446 63.5394 2.446C63.1847 2.446 62.8347 2.56267 62.4894 2.796C62.1534 3.02933 61.864 3.35133 61.6214 3.762L60.9774 3.482C61.164 2.894 61.584 2.34333 62.2374 1.83C62.9 1.31667 63.6327 1.06 64.4354 1.06C65.2474 1.06 65.8774 1.27933 66.3254 1.718C66.7827 2.15667 67.0114 2.70733 67.0114 3.37C67.0114 3.846 66.8714 4.36867 66.5914 4.938C66.3207 5.498 65.9707 6.03467 65.5414 6.548C64.7387 7.49067 63.936 8.284 63.1334 8.928C64.3 9.24533 65.2007 9.404 65.8354 9.404C66.0127 9.404 66.1434 9.35733 66.2274 9.264C66.3114 9.16133 66.442 8.93267 66.6194 8.578L67.0814 8.676C66.988 10.3933 66.414 11.252 65.3594 11.252ZM74.993 4.672C74.993 6.24 74.643 7.67733 73.943 8.984C73.5977 9.63733 73.1263 10.1647 72.529 10.566C71.941 10.9673 71.2737 11.168 70.527 11.168C69.6217 11.168 68.9403 10.8367 68.483 10.174C68.0257 9.502 67.797 8.61533 67.797 7.514C67.797 5.61 68.2123 4.06067 69.043 2.866C69.8737 1.662 70.9423 1.06 72.249 1.06C73.1823 1.06 73.873 1.40067 74.321 2.082C74.769 2.754 74.993 3.61733 74.993 4.672ZM70.653 10.23C71.073 10.23 71.437 9.95933 71.745 9.418C72.0623 8.86733 72.291 8.2 72.431 7.416C72.7017 5.88533 72.837 4.73733 72.837 3.972C72.837 3.19733 72.7717 2.67933 72.641 2.418C72.5197 2.14733 72.2957 2.012 71.969 2.012C71.6517 2.012 71.353 2.23133 71.073 2.67C70.8023 3.10867 70.5877 3.67333 70.429 4.364C70.1117 5.72667 69.953 7.108 69.953 8.508C69.953 9.18 70.0137 9.63733 70.135 9.88C70.2563 10.1133 70.429 10.23 70.653 10.23ZM78.3714 0.71C78.9407 0.71 79.3607 0.892 79.6314 1.256C79.9114 1.62 80.0514 2.082 80.0514 2.642C80.0514 3.19267 79.9627 3.72467 79.7854 4.238C79.608 4.742 79.3 5.18067 78.8614 5.554C78.432 5.92733 77.928 6.114 77.3494 6.114C76.78 6.114 76.3507 5.93667 76.0614 5.582C75.7814 5.218 75.6414 4.742 75.6414 4.154C75.6414 3.13667 75.8934 2.31067 76.3974 1.676C76.9107 1.032 77.5687 0.71 78.3714 0.71ZM77.5174 5.47C77.8534 5.47 78.1194 5.008 78.3154 4.084C78.4274 3.56133 78.4834 2.978 78.4834 2.334C78.4834 1.68067 78.362 1.354 78.1194 1.354C77.8114 1.354 77.5594 1.816 77.3634 2.74C77.2607 3.272 77.2094 3.86 77.2094 4.504C77.2094 5.148 77.312 5.47 77.5174 5.47ZM84.6714 5.792C85.2407 5.792 85.6607 5.974 85.9314 6.338C86.2114 6.702 86.3514 7.164 86.3514 7.724C86.3514 8.27467 86.2627 8.80667 86.0854 9.32C85.908 9.824 85.6 10.2627 85.1614 10.636C84.732 11.0093 84.228 11.196 83.6494 11.196C83.0801 11.196 82.6507 11.0187 82.3614 10.664C82.0814 10.3 81.9414 9.824 81.9414 9.236C81.9414 8.21867 82.1934 7.39267 82.6974 6.758C83.2107 6.114 83.8687 5.792 84.6714 5.792ZM83.8174 10.552C84.1534 10.552 84.4194 10.09 84.6154 9.166C84.7274 8.64333 84.7834 8.06 84.7834 7.416C84.7834 6.76267 84.662 6.436 84.4194 6.436C84.1114 6.436 83.8594 6.90267 83.6634 7.836C83.5607 8.35867 83.5094 8.942 83.5094 9.586C83.5094 10.23 83.612 10.552 83.8174 10.552ZM84.0694 1.186C83.6307 2.18467 83.094 3.272 82.4594 4.448C81.834 5.61467 81.2647 6.73 80.7514 7.794C80.238 8.84867 79.7854 9.96867 79.3934 11.154C79.2347 11.1447 79.034 11.0933 78.7914 11C78.53 10.9067 78.306 10.7807 78.1194 10.622C78.558 9.38067 79.3047 7.72867 80.3594 5.666C81.414 3.60333 82.1794 1.956 82.6554 0.724C83.3554 0.863999 83.8267 1.018 84.0694 1.186Z"
        fill="#F97316"
      />
      <path
        d="M34.0675 19.5213C33.6826 18.4273 33.5583 18.3071 32.8237 18.2459C32.1869 18.183 31.7256 18.3863 30.6877 19.2104C28.2493 21.1092 27.9578 22.7034 30.3509 21.0255C31.0518 20.5249 31.6822 20.1965 31.7567 20.2687C32.0301 20.5331 31.1488 23.4332 30.2962 25.1102C27.81 30.0419 22.5849 33.4784 18.9029 32.6345C17.7242 32.3361 17.1073 32.0039 17.1013 31.6372C17.0993 31.515 17.4094 31.0452 17.8177 30.5983C19.5684 28.5153 19.94 25.8437 18.6053 24.9855C17.6658 24.3652 15.3848 25.4299 14.4311 26.9129C13.8598 27.8516 13.7618 29.3204 14.2228 30.5844L14.6326 31.7025L12.8861 32.5627C9.61205 34.2063 6.51417 34.6732 3.69026 33.9618C2.95362 33.7784 2.02181 33.6226 1.60622 33.6295L0.873214 33.666L1.44317 34.1212C2.16182 34.6962 3.98018 35.2286 5.89067 35.4171C7.82561 35.6052 10.8465 34.9195 13.5403 33.6768L15.6759 32.6878L16.9336 33.3273C22.1115 35.9072 30.002 30.9839 32.1339 23.8571C32.4127 22.9722 32.6916 22.0872 32.7372 21.8909C32.8065 21.6452 33.0041 21.7642 33.3557 22.3208C33.6318 22.7564 33.9794 23.0686 34.1493 22.9924C34.6113 22.838 34.5542 20.8582 34.0675 19.5213ZM17.1122 29.3384C16.3004 30.5256 15.6175 30.6347 15.332 29.6368C15.0205 28.5415 15.6816 27.1123 16.7968 26.5315C17.9361 25.9257 17.9361 25.9257 18.0428 26.462C18.2004 27.1196 17.8527 28.2747 17.1122 29.3384Z"
        fill="#FA8F45"
      />
    </svg>
  );
};

type IconType =
  | "feature-bullets"
  | "money"
  | "filter"
  | "export"
  | "move-down"
  | "move-right"
  | "move-left"
  | "billing-label";

type Icon = {
  name: IconType;
  svgProps: React.SVGProps<SVGSVGElement>;
};

const Icons: React.FC<Icon> = ({ name, svgProps }) => {
  const icons: Record<IconType, JSX.Element> = {
    "feature-bullets": <FeatureBullets {...svgProps} />,
    money: <Money {...svgProps} />,
    filter: <Filter {...svgProps} />,
    export: <Export {...svgProps} />,
    "move-down": <MoveDown {...svgProps} />,
    "move-right": <MoveRight {...svgProps} />,
    "move-left": <MoveLeft {...svgProps} />,
    "billing-label": <BillingLabel {...svgProps} />,
  };

  return icons[name];
};

export default Icons;
