import React, { use<PERSON>ontext, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogTitle,
} from "~/components/ui/dialog";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { X, LinkIcon } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "~/components/ui/tabs";
import InviteFew from "./invite-few";
import InviteMany from "./invite-many";
import cogoToast from "cogo-toast";
import { DataContext } from "~/store/GlobalState";
import { PostRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";

interface StorageOffloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  // eslint-disable-next-line
  onInviteSuccess: (invitedUsers: any[]) => void;
}
interface User {
  id: string;
  email: string;
  role: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  organisation_id: string | null;
  is_default: boolean;
  permissions: {
    id: string;
    role_id: string;
    permission_list: {
      can_remove_people_from_organization: boolean;
      can_invite_members: boolean;
      can_create_custom_role: boolean;
      can_create_channel: boolean;
      can_comment_on_threads: boolean;
      can_view_billing: boolean;
      can_create_webhooks: boolean;
      can_view_channels: boolean;
      can_change_user_org_role: boolean;
    };
    is_default: boolean;
  };
}

export interface InviteUser {
  email: string;
  role: string;
}

const InviteModal: React.FC<StorageOffloadModalProps> = ({
  isOpen,
  onClose,
  onInviteSuccess,
}) => {
  const [activeTab, setActiveTab] = useState("invite-few");
  const [roleId, setRoleId] = useState("");
  const [roles, setRoles] = useState<Role[]>([]);
  const [users, setUsers] = useState<User[]>([
    { id: "1", email: "", role: "" },
  ]);
  const [emails, setEmails] = useState<string[]>([]);
  const [buttonLoading, setButtonLoading] = useState(false);
  const [inviteFewUsers, setInviteFewUsers] = useState<InviteUser[]>([]);

  const { state } = useContext(DataContext);
  const { orgId, orgRoles } = state;

  // Helper function to create optimistic invite data
  const createOptimisticInvites = (emails: string[], roleName: string) => {
    return emails.map((email) => ({
      id: `temp-${Date.now()}-${Math.random()}`,
      email,
      username: "",
      phone_number: "",
      profile_url: "",
      name: "",
      role: roleName,
      status: "invited" as const,
      created_at: new Date().toISOString(),
      entity_type: "user" as const,
    }));
  };

  const handleCopyInviteLink = async () => {
    if (!orgId) return;

    const payload = {
      organisation_id: orgId,
      role_id: roleId,
    };

    try {
      const res = await PostRequest("/invite/general", payload);

      if (res?.status === 200 || res?.status === 201) {
        const link = res?.data?.data?.invitation_link;
        await navigator.clipboard.writeText(link);
        cogoToast.success("Invite link copied to clipboard");
      } else {
        cogoToast.error("Failed to generate invite link");
      }
    } catch (error) {
      console.error(error);
      cogoToast.error("An error occurred while copying the invite link");
    }
  };

  const handleSubmitFewInvite = async () => {
    if (!orgId || !roleId) return;

    setButtonLoading(true);

    const payload = {
      org_id: orgId,
      invitations: inviteFewUsers,
    };

    const res = await PostRequest("/invite/invite-few", payload);
    if (res?.status === 200 || res?.status === 201) {
      cogoToast.success(res?.data?.message);

      // Optimistically add invited users to the table
      if (onInviteSuccess) {
        const emails = inviteFewUsers.map((user) => user.email);
        const roleName =
          roles.find((role) => role.id === roleId)?.name || "User";
        const optimisticInvites = createOptimisticInvites(emails, roleName);
        onInviteSuccess(optimisticInvites);
      }

      onClose();
    }
    setButtonLoading(false);
  };

  const handleSubmitManyInvite = async () => {
    if (!orgId || !roleId) return;

    setButtonLoading(true);

    const payload = {
      org_id: orgId,
      emails: emails,
      role_id: roleId,
    };

    const res = await PostRequest("/invite", payload);
    if (res?.status === 200 || res?.status === 201) {
      cogoToast.success(res?.data?.message);

      // Optimistically add invited users to the table
      if (onInviteSuccess) {
        const roleName =
          roles.find((role) => role.id === roleId)?.name || "User";
        const optimisticInvites = createOptimisticInvites(emails, roleName);
        onInviteSuccess(optimisticInvites);
      }

      onClose();
    }

    setButtonLoading(false);
  };

  useEffect(() => {
    setRoles(orgRoles);
    const roleuser = orgRoles?.find((item: Role) => item?.name === "User");
    setRoleId(roleuser?.id);
  }, [orgId]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="backdrop-blur-sm bg-black/10">
        <DialogContent className="sm:max-w-2xl bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden">
          <div className="relative">
            <DialogHeader className="px-6 pt-6 pb-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <DialogTitle className="text-lg font-semibold text-gray-900">
                  Invite Your Team
                </DialogTitle>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className=" p-1 rounded-md border hover:bg-gray-100"
                >
                  <X size={20} color="#344054" />
                </Button>
              </div>
            </DialogHeader>

            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full mt-0"
            >
              <div className="border-b px-6 border-gray-200">
                <TabsList className="flex w-fit bg-white rounded-none h-auto p-0 space-x-10">
                  <TabsTrigger
                    value="invite-few"
                    className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2"
                  >
                    Invite Few
                  </TabsTrigger>
                  <TabsTrigger
                    value="invite-many"
                    className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2"
                    data-testid="invite-many-tab"
                  >
                    Invite Many
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="invite-few" className="mt-6">
                <InviteFew
                  users={users}
                  setUsers={setUsers}
                  roles={roles}
                  onUsersChange={setInviteFewUsers}
                />
              </TabsContent>

              <TabsContent value="invite-many" className="mt-6 ">
                <InviteMany emails={emails} setEmails={setEmails} />
              </TabsContent>
            </Tabs>

            <div className="flex  justify-between items-center px-4 mt-4">
              <div className="w-full h-[0.5px] bg-[#D6DAE0] !mt-0" />
            </div>

            <div className="!mt-0 p-4">
              {activeTab === "invite-few" ? (
                <>
                  <p className="text-sm text-gray-500">
                    <span className="font-semibold text-black">Note:</span> You
                    can also copy the invite link to invite members of your team
                    as <span className="font-semibold text-black">"Users"</span>{" "}
                    and give them basic access.
                  </p>
                </>
              ) : (
                <>
                  {" "}
                  <p className="text-sm text-gray-500">
                    <span className="font-semibold text-black">Note:</span> This
                    automatically invites all members as{" "}
                    <span className="font-semibold text-black">"Users"</span>{" "}
                    and gives them basic access. Copying the invite link to
                    share also does this.
                  </p>
                </>
              )}
            </div>
            <div className="flex flex-col sm:flex-row justify-between gap-3 !mt-0 px-4 pb-4">
              <Button
                variant="outline"
                className="h-9 border border-[#7141F8]/40 w-full sm:w-auto"
                onClick={handleCopyInviteLink}
              >
                <LinkIcon size={16} color="#7141F8" />
                <span className="ml-2 text-[#7141F8]">Copy invite link</span>
              </Button>

              <div className="flex gap-3 w-full sm:w-auto">
                <Button
                  variant={"outline"}
                  className="h-9 flex-1 sm:flex-none"
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button
                  className="bg-[#7141F8] h-9 text-white px-7 gap-3 flex-1 sm:flex-none"
                  disabled={
                    (activeTab === "invite-few" &&
                      !users.some((user) => user.email && user.role)) ||
                    (activeTab === "invite-many" && emails.length === 0) ||
                    buttonLoading
                  }
                  onClick={
                    activeTab === "invite-few"
                      ? handleSubmitFewInvite
                      : handleSubmitManyInvite
                  }
                >
                  Send Invite{" "}
                  {buttonLoading && <Loading width="14" height="14" />}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  );
};

export default InviteModal;
