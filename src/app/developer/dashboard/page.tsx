"use client";
import { useState, useEffect } from "react";
import Loading from "~/components/ui/loading";
import UserCard from "~/components/developer/userCard";
import { PlusCircleIcon } from "lucide-react";
import Image from "next/image";
import { GetRequest } from "~/utils/request";
import { Button } from "~/components/ui/button";
import { CreateAgentModal } from "~/components/modals/CreateAgentModal";
import { AgentDetailsModal } from "~/components/modals/AgentDetailsModal";

function Dashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [agents, setAgents] = useState<any>([]);

  const [selectedAgent, setSelectedAgent] = useState(null);
  const [isAgentModalOpen, setIsAgentModalOpen] = useState(false);

  const openAgentModal = (agent: any) => {
    setSelectedAgent(agent);
    setIsAgentModalOpen(true);
  };

  const closeAgentModal = () => {
    setIsAgentModalOpen(false);
    setSelectedAgent(null);
  };

  useEffect(() => {
    const token = localStorage.getItem("token") || "";
    // setIsLoading(true)

    const getAgents = async () => {
      const response = await GetRequest(`/agents/me`, token);

      if (response?.status === 200) {
        setAgents(response?.data?.data);
      } else {
        setAgents([]);
      }

      setIsLoading(false);
    };
    getAgents();
  }, []);

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      {isModalOpen && <CreateAgentModal closeModal={closeModal} />}

      {isAgentModalOpen && selectedAgent && (
        <AgentDetailsModal agent={selectedAgent} closeModal={closeAgentModal} />
      )}

      <div className="mx-auto xl:max-w-[1440px] grow max-w-screen">
        <div className="p-3 flex items-center justify-between">
          <div className="flex flex-col mt-6 mb-10">
            <div className="flex gap-2">
              <Image src="/TelexIcon.svg" alt="Icon" width={40} height={40} />
              <h1 className="font-semibold text-4xl text-center flex justify-center items-center max-lg:text-2xl">
                Telex Developer
              </h1>
            </div>
          </div>
          <UserCard />
        </div>

        <main className="w-full pb-6 px-6 md:pt-3 md:pb-16">
          {isLoading && (
            <div className="flex items-center justify-center mt-20">
              <Loading width="50" height="50" color="#6B46FF" />
            </div>
          )}
          {agents.length == 0 && !isLoading ? (
            <>
              <div className="w-full">
                <section className="w-full max-w-2xl px-5 mx-auto">
                  <div className="flex flex-col justify-center items-center mt-20">
                    <div className="flex gap-2 justify-center">
                      <Image
                        src="/TelexIcon.svg"
                        alt="Icon"
                        width={40}
                        height={40}
                      />
                      <h1 className="font-semibold text-4xl text-center flex justify-center items-center max-lg:text-2xl">
                        Telex
                      </h1>
                    </div>
                  </div>

                  <div className="mt-[50px] text-center">
                    <h1 className="font-semibold mb-3 leading-8 text-[46px] max-lg:text-3xl max-lg:font-bold">
                      Welcome to Telex Developer
                    </h1>
                    <p className="my-8 text-center text-md md:text-lg text-balance text-[rgba(110,110,111,1)]">
                      Seamlessly create and manage intelligent agents to power
                      real-time automation, communication, and integration
                      across your organization.
                    </p>

                    <div>
                      <Button
                        onClick={openModal}
                        className=" bg-blue-400 w-[180px] py-6 px-10 text-white text-base font-medium hover hover:bg-blue-300"
                      >
                        Create an Agent
                      </Button>
                    </div>

                    <p className="text-sm md:text-md text-left sm:text-center text-[rgba(110,110,111,1)] my-6 lg:w-[450px] mx-auto">
                      By continuing, you are agreeing to our Privacy Policy,
                      Main Service Agreement, Terms of Service, and Cookie
                      Policy.
                    </p>
                  </div>
                </section>
              </div>
            </>
          ) : (
            <div>
              <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {agents.map((agent: any, index: number) => {
                  return (
                    <div
                      onClick={() => openAgentModal(agent)}
                      key={index}
                      className="border border-gray-300 p-4 rounded hover:bg-gray-100 cursor-pointer transition-colors"
                    >
                      <h1 className="font-bold">{agent.app_name}</h1>
                      <p>{agent.is_active ? "Active" : "Inactive"}</p>
                      <a href={agent.json_url} className="mt-3">
                        {agent.json_url}
                      </a>
                      <p className="text-[rgba(110,110,111,1)] mt-5">
                        {agent.app_description}
                      </p>
                    </div>
                  );
                })}

                {!isLoading && (
                  <div
                    onClick={openModal}
                    className="flex flex-col group  items-center justify-center border border-gray-300 cursor-pointer p-4 text-center rounded hover:bg-gray-100 transition-colors"
                  >
                    <h3 className="font-bold">Create an Agent</h3>
                    <p className="text-[rgba(110,110,111,1)]">
                      Create a new organisational agent
                    </p>
                    <PlusCircleIcon className="group-hover:fill-white group-hover:stroke-[#7b50fb] mt-5" />
                  </div>
                )}
              </div>
            </div>
          )}
        </main>
      </div>
    </>
  );
}

export default Dashboard;
