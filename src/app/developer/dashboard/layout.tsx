"use client";
import { useRouter } from "next/navigation";
import React, { useContext, useEffect, useState } from "react";
import { ACTIONS } from "~/store/Actions";
import { DataContext, DataProvider } from "~/store/GlobalState";
import SideBar from "~/components/developer/Sidebar";
import ErrorBoundary from "~/components/error-boundary/Error-boundary";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { dispatch } = useContext(DataContext);

  useEffect(() => {
    const token = localStorage.getItem("token");
    dispatch({ type: ACTIONS.TOKEN, payload: token });

    if (!token) {
      router.push("/developer/login");
      return;
    }

    setLoading(false);
  }, [dispatch, router]);

  if (loading) return;

  return (
    <DataProvider>
      <ErrorBoundary>
        <div className="w-full flex relative">
          <SideBar />

          <div className={`md:ml-[110px] w-full relative`}>{children}</div>
        </div>
      </ErrorBoundary>
    </DataProvider>
  );
}
