"use server";
import React from "react";
import <PERSON> from "../../components/application-monitoring/Hero";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import ImageBg from "../../_assets/mobile-application-bg.svg";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import MonitorComponent from "~/app/(external-pages)/products/components/single-product/Monitor";

const data = [
  {
    id: 1,
    title: "Real-Time App Performance Monitoring",
    content: `Track app responsiveness, load times, and background tasks.`,
  },
  {
    id: 2,
    title: "Monitoring Crash Detection and Reporting",
    content: `Instantly detect and diagnose crashes with detailed reports and stack traces.`,
  },
  {
    id: 3,
    title: "Monitor User Interaction Analytics",
    content: `Understand how users are interacting with your app to improve UX.`,
  },
  {
    id: 4,
    title: "Network Monitoring",
    content: `Monitor data usage, API calls, and latency to ensure smooth communication between your app and backend.`,
  },
  {
    id: 5,
    title: "Track Battery & Resource Usage",
    content: `Optimize app performance by tracking CPU, memory, and battery consumption on mobile devices.`,
  },
];

const firstData = [
  {
    id: 1,
    title: "iOS (Swift, Objective-C)",
    content: `Integrate Telex with iOS apps to monitor performance metrics, crashes, and user interactions across iPhone and iPad devices.`,
    link: "",
  },
  {
    id: 2,
    title: "Android (Kotlin, Java)",
    content: `Monitor Android apps by integrating Telex, tracking performance, crashes, battery usage, and network calls across various Android devices and OS versions.`,
    link: "",
  },
  {
    id: 3,
    title: "Monitor User Interaction Analytics",
    content: `Understand how users are interacting with your app to improve UX.`,
    link: "",
  },
];

const secondData = [
  {
    id: 1,
    title: "React Native",
    content: `With Telex integration, you can monitor performance and crashes in React Native apps, allowing you to track user engagement and app stability across both iOS and Android.`,
    link: "",
  },
  {
    id: 2,
    title: "Flutter",
    content: `Integrate Telex into your Flutter applications to gain insights into crashes, performance, and user behavior in a single codebase for both platforms.`,
    link: "",
  },
  {
    id: 3,
    title: "Xamarin",
    content: `Telex can monitor Xamarin apps, helping developers identify issues in mobile apps built using C# and .NET for iOS and Android.`,
    link: "",
  },
];

const thirdData = [
  {
    id: 1,
    title: "Firebase",
    content: `Use Telex to complement Firebase services by tracking deeper performance metrics, monitoring network requests, and identifying backend issues affecting mobile apps.`,
    link: "",
  },
  {
    id: 2,
    title: "GraphQL",
    content: `Monitor API performance, request latency, and data fetching in apps using GraphQL for mobile backend services.`,
    link: "",
  },
  {
    id: 3,
    title: "RESTful APIs",
    content: `Track the performance of RESTful API calls made by your mobile app, ensuring smooth communication between the app and the backend servers.`,
    link: "",
  },
];

//

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Mobile Application Monitoring"
        title="Ensure Peak Performance for Your Mobile App with Telex"
        content="Real-Time Monitoring and Actionable Insights to Keep Your App Running Smoothly."
        routeLink=""
        routeName=""
        imageBg={ImageBg?.src}
        width="60%"
      />
      <SecondFeatures
        heading="Optimize User Experience and Performance with Telex"
        content="mobile app performance can make or break user satisfaction. Telex provides a complete suite of monitoring tools designed to help you identify performance issues, crashes, and bottlenecks before they impact your users."
        items={data}
      />
      <MonitorComponent
        heading="Native Mobile Platforms Monitoring"
        items={firstData}
      />
      <MonitorComponent
        heading="Cross-Platform Mobile Frameworks Monitoring"
        items={secondData}
      />
      <MonitorComponent
        heading="Monitor Backend Technologies for Mobile Apps"
        items={thirdData}
      />
      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto">
          <Faq />
          <KeepTrack
            title="Monitor and optimize Google Cloud workloads"
            content="Experience the benefits firsthand and discover a more efficient way to optimize your Google Cloud."
          />
        </div>
      </div>
    </>
  );
};

export default Page;
