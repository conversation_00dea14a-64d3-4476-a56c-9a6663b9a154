// "use server";
import React from "react";
import <PERSON> from "../../components/application-monitoring/Hero";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import ImageBg from "../../_assets/database-system-bg.svg";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import MonitorComponent from "~/app/(external-pages)/products/components/single-product/Monitor";
import Mysql from "../../_assets/mysql.svg";
import Postgre from "../../_assets/postgre.svg";
import Mongodb from "../../_assets/mongodb.svg";
import Redis from "../../_assets/redis.svg";
import Microsoft from "../../_assets/sqlserver.svg";
import Oracle from "../../_assets/oracle.svg";
import { DatabaseFaq } from "../../data/database-faqs";

const data = [
  {
    id: 1,
    title: "Real-Time Performance Analytics",
    content: `Telex offers in-depth performance metrics for each query and transaction, helping individuals monitor database performance in real time and identify bottlenecks swiftly.`,
  },
  {
    id: 2,
    title: "Automated Query Optimization",
    content: `With Telex, track the efficiency of all queries, get insights on slow queries, and receive suggestions on indexing or optimization to improve database speed.`,
  },
  {
    id: 3,
    title: "Instant Alerts on Anomalies and Errors",
    content: `Telex sends immediate notifications for critical issues, such as failed queries or connection issues, ensuring individuals can take quick action to prevent downtime.`,
  },
  {
    id: 4,
    title: "Detailed Resource Utilization Tracking",
    content: `Telex monitors CPU, memory, and disk I/O usage in your database, helping you maintain optimal resource allocation and prevent performance lags.`,
  },
  {
    id: 5,
    title: "Comprehensive User Activity Audits",
    content: `Track changes, logins, and activity logs to keep a close eye on who accesses your data, enhancing security and preventing unauthorized access.`,
  },
  {
    id: 6,
    title: "Historical Data Insights and Trends",
    content: `Telex enables users to analyze historical performance data, detect trends, and make proactive adjustments to enhance database performance over time.`,
  },
];

const seconddata = [
  {
    id: 1,
    title: "Connect Your Systems",
    content: `Start by connecting your ERP, CRM, and internal systems to Telex. Our platform supports both native integrations and custom API connections to suit your specific infrastructure.`,
    link: "",
  },
  {
    id: 2,
    title: "Define Key Metrics",
    content: `Define the KPIs and performance indicators you need to monitor. From system uptime to resource usage and user interactions, Telex offers comprehensive tracking capabilities.`,
    link: "",
  },
  {
    id: 3,
    title: "Real-Time Monitoring and Alerts",
    content: `Telex immediately begins monitoring your systems, offering real-time data and sending notifications if something goes wrong. Customize alerts based on critical thresholds to ensure you're always aware of potential issues.`,
    link: "",
  },
];

const technologies = [
  {
    id: 1,
    title: "Monitor MySQL Databases",
    content:
      "Telex provides in-depth insights for MySQL databases, including query analysis, indexing recommendations, and performance tracking for open-source SQL environments.",
    link: "",
    image: Mysql,
  },
  {
    id: 2,
    title: "Monitor PostgreSQL Databases",
    content:
      "From advanced indexing to transaction monitoring, Telex ensures that PostgreSQL databases maintain high efficiency, tracking every process and alerting users to anomalies.",
    link: "",
    image: Postgre,
  },
  {
    id: 3,
    title: "Monitor MongoDB Databases",
    content:
      "Telex monitors MongoDB’s NoSQL structure, providing document-level analytics, usage statistics, and alerts on replica set issues and memory usage.",
    link: "",
    image: Mongodb,
  },
  {
    id: 4,
    title: "Monitor Redis Databases",
    content:
      "Telex’s real-time insights into Redis operations cover data structure statistics, memory efficiency, and cache hit rates, ensuring smooth cache performance.",
    link: "",
    image: Redis,
  },
  {
    id: 5,
    title: "Monitor SQL Server Databases",
    content:
      "With advanced monitoring for SQL Server, Telex tracks stored procedures, optimizes query execution plans, and provides detailed statistics on transaction log usage",
    link: "",
    image: Microsoft,
  },
  {
    id: 6,
    title: "Monitor Oracle Databases",
    content:
      "Telex tracks Oracle-specific performance metrics, offering in-depth analysis of tablespaces, resource locking, and execution plans for high-demand environments.",
    link: "",
    image: Oracle,
  },
];

//

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Database Monitoring"
        title="Maximize Database Performance and Uptime with Telex Monitoring"
        content="From query optimization to real-time error notifications, Telex ensures your databases perform reliably, securely, and efficiently."
        routeLink=""
        routeName=""
        imageBg={ImageBg?.src}
      />

      <SecondFeatures
        heading="Get in-depth monitoring you need to ensure smooth operations"
        content="In complex environments, where multiple systems interact, it’s critical to maintain real-time visibility across every component, from infrastructure to user interactions"
        items={data}
      />

      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto pt-[80px] pb-[40px]">
          <h1 className="text-2xl md:text-3xl lg:text-[32px] font-semibold mb-5 lg:leading-snug md:w-[80%] lg:w-[50%]">
            How Telex Supports Database Monitoring
          </h1>

          <div
            className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-14 mt-10`}
          >
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Proactive Maintenance for Consistent Uptime
              </h2>
              <p className="text-gray-500 mb-4">
                Telex helps prevent unplanned downtime by continuously
                monitoring database health, ensuring businesses maintain
                consistent service availability.
              </p>
            </div>
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Improved Data Security Compliance
              </h2>
              <p className="text-gray-500 mb-4">
                Telex’s audit trails and access monitoring features support
                compliance with industry regulations, making it easier for
                businesses to protect data integrity.
              </p>
            </div>
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Cost-Efficient Resource Management
              </h2>
              <p className="text-gray-500 mb-4">
                By tracking resource usage and load patterns, Telex helps
                businesses optimize database operations, avoiding
                over-provisioning and reducing unnecessary costs.
              </p>
            </div>
          </div>
        </div>
      </div>

      <MonitorComponent
        heading="Integration with Leading Database technologies "
        items={technologies}
        showlink={false}
      />

      <MonitorComponent
        heading="How Telex Works for Enterprise Systems:"
        items={seconddata}
      />

      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto">
          <Faq faq={DatabaseFaq} />
          <KeepTrack
            title="Ensure peak performance and security with Telex Database Monitoring"
            content="Start today to optimize your Database Performances"
          />
        </div>
      </div>
    </>
  );
};

export default Page;
