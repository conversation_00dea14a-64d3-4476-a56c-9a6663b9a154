// "use server";
import React from "react";
import <PERSON> from "../../components/application-monitoring/Hero";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import ImageBg from "../../_assets/microservices-bg.svg";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import MonitorComponent from "~/app/(external-pages)/products/components/single-product/Monitor";
import { MicroserviceFaq } from "../../data/microservices-faqs";
import Docker from "../../_assets/docker.svg";
import Apache from "../../_assets/apache.svg";
import Istilo from "../../_assets/istilo.svg";

const data = [
  {
    id: 1,
    title: "Distributed Tracing Across Services",
    content: `Telex offers deep traceability by tracking each request across the microservices architecture. This lets developers and engineers understand latency points, interdependencies, and bottlenecks within individual microservices, making it easier to resolve issues quickly.`,
  },
  {
    id: 2,
    title: "Real-Time Error and Exception Alerts",
    content: `With Telex’s real-time error tracking, users get immediate notifications of service failures or critical issues within any microservice. This prevents downtimes, allowing individuals to fix problems promptly and ensure optimal service operation.`,
  },
  {
    id: 3,
    title: "Performance Metrics per Service",
    content: `Telex tracks and visualizes performance metrics, including latency, error rates, and request processing times for each microservice. Individuals can drill down into each metric to evaluate resource efficiency and response times, identifying and resolving slow-running services.`,
  },
  {
    id: 4,
    title: "Resource Utilization Insights",
    content: `Monitoring CPU, memory, and network bandwidth per service allows users to efficiently allocate resources based on demand, reducing overuse or bottlenecks within the microservices ecosystem.`,
  },
  {
    id: 5,
    title: "Dependency Mapping and Analysis",
    content: `Telex maps out dependencies between microservices, making it easy to identify potential points of failure. By understanding these relationships, individuals can better plan updates, troubleshoot issues, and ensure high availability for critical services.`,
  },
  {
    id: 6,
    title: "Automated Health Checks and Status Reports",
    content: `With regular health checks and automated status updates, Telex ensures that all services are running smoothly. These checks help detect any instability early, improving reliability and reducing troubleshooting times for developers and engineers.`,
  },
];

const seconddata = [
  {
    id: 1,
    title: "Connect Your Systems",
    content: `Start by connecting your ERP, CRM, and internal systems to Telex. Our platform supports both native integrations and custom API connections to suit your specific infrastructure.`,
    link: "",
  },
  {
    id: 2,
    title: "Define Key Metrics",
    content: `Define the KPIs and performance indicators you need to monitor. From system uptime to resource usage and user interactions, Telex offers comprehensive tracking capabilities.`,
    link: "",
  },
  {
    id: 3,
    title: "Real-Time Monitoring and Alerts",
    content: `Telex immediately begins monitoring your systems, offering real-time data and sending notifications if something goes wrong. Customize alerts based on critical thresholds to ensure you're always aware of potential issues.`,
    link: "",
  },
];

const technologies = [
  {
    id: 1,
    title: "Docker & Kubernetes",
    content:
      "Telex integrates seamlessly with Docker and Kubernetes, providing visibility into containerized services, resource allocations, and orchestration health. This enables users to monitor service reliability within containerized environments.",
    link: "",
    image: Docker,
  },
  {
    id: 2,
    title: "Apache Kafka",
    content:
      "By integrating with Kafka, Telex offers insights into data flow and message queue processing, helping to monitor performance and ensure reliable data streams across microservices.",
    link: "",
    image: Apache,
  },
  {
    id: 3,
    title: "Envoy & Istio",
    content:
      "Telex works with Envoy and Istio to monitor network traffic, latency, and request routing between microservices, allowing developers to optimize communication paths and avoid traffic bottlenecks",
    link: "",
    image: Istilo,
  },
];

//

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Monitoring Microservices"
        title="Streamline Microservices Monitoring with Telex for Unmatched Performance and Stability"
        content="Telex helps you manage and monitor complex microservices architectures, providing real-time insights, detailed error reporting, and end-to-end traceability."
        routeLink=""
        routeName=""
        imageBg={ImageBg?.src}
      />

      <SecondFeatures
        heading="Get in-depth monitoring you need to ensure smooth operations"
        content="In complex environments, where multiple systems interact, it’s critical to maintain real-time visibility across every component, from infrastructure to user interactions"
        items={data}
      />

      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto pt-[80px] pb-[40px]">
          <h1 className="text-2xl md:text-3xl lg:text-[32px] font-semibold mb-5 lg:leading-snug md:w-[80%] lg:w-[50%]">
            How Telex Supports Your Microservices monitoring
          </h1>

          <div
            className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-14 mt-10`}
          >
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Improved Incident Response and Service Availability
              </h2>
              <p className="text-gray-500 mb-4">
                Telex enables teams to identify and resolve incidents faster
                with real-time alerts, preventing costly downtimes. Increased
                service availability improves user experience, builds customer
                trust, and reduces operational costs associated with unplanned
                outages.
              </p>
            </div>
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Optimized Performance and Cost Management
              </h2>
              <p className="text-gray-500 mb-4">
                Businesses can use Telex’s performance data to allocate
                resources more effectively across microservices, minimizing
                overhead and optimizing cloud costs. This makes it easier to
                maintain budgets while delivering high-performing applications
                to end-users.
              </p>
            </div>
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                End-to-End Compliance and Security Monitoring
              </h2>
              <p className="text-gray-500 mb-4">
                For businesses that handle sensitive data, Telex monitors
                compliance across each microservice layer, detecting
                unauthorized access attempts and ensuring consistent security
                practices. This reduces risk and simplifies regulatory reporting
                requirements.
              </p>
            </div>
          </div>
        </div>
      </div>

      <MonitorComponent
        heading="Integration with Leading Microservices technologies"
        items={technologies}
        showlink={false}
      />

      <MonitorComponent
        heading="How Telex Works for Enterprise Systems:"
        items={seconddata}
      />

      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto">
          <Faq faq={MicroserviceFaq} />
          <KeepTrack
            title="Get started with Telex and elevate your microservices performance."
            content="Experience real-time monitoring built for modern, scalable applications!"
          />
        </div>
      </div>
    </>
  );
};

export default Page;
