"use server";
import React from "react";
import <PERSON> from "../../components/application-monitoring/Hero";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import ImageBg from "../../_assets/monitor-web-bg.svg";
import ImageSection from "../../components/application-monitoring/ImageSection";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import SectionImage from "../../_assets/sectionImage.svg";

const data = [
  {
    id: 1,
    title: "Monitor Application Server Health & Uptime",
    content: `monitor your Go server's uptime, response times, and overall availability, alerting you in case of downtime or slow response times.`,
  },
  {
    id: 2,
    title: "Monitor Application Logs",
    content: `track and centralize application logs from different parts of your Go app. This helps you identify issues like errors, warnings, or unexpected behavior in real-time.`,
  },
  {
    id: 3,
    title: "Performance Metrics Insights Monitoring",
    content: `Monitoring CPU usage, memory consumption, and goroutine count ensures that the Go application is performing optimally without unnecessary resource consumption.`,
  },
  {
    id: 4,
    title: "Monitor HTTP Requests",
    content: `Telex can log and monitor incoming HTTP requests, response times, request counts, and errors. It can help track things like 500 internal server errors, slow endpoints, and status codes.`,
  },
  {
    id: 5,
    title: "Monitor Database Performance",
    content: `For Go applications using databases (e.g., PostgreSQL, MySQL), Telex can monitor database queries, query response times, and potential connection issues.`,
  },
  {
    id: 6,
    title: "Track Errors",
    content: `Monitoring runtime errors and panics allows developers to quickly address exceptions and bugs that occur within the Go application.`,
  },
  {
    id: 7,
    title: "Customize metrics",
    content: `You can set up custom application metrics to track domain-specific data, like the number of users, transactions processed, or other business-critical events.`,
  },
  {
    id: 8,
    title: "Monitor Third-party API Integrations",
    content: `For Go applications using databases (e.g., PostgreSQL, MySQL), Telex can monitor database queries, query response times, and potential connection issues.`,
  },
  {
    id: 9,
    title: "Monitor Security Events",
    content: `Monitoring runtime errors and panics allows developers to quickly address exceptions and bugs that occur within the Go application.`,
  },
];

const seconddata = [
  {
    id: 1,
    title: "Setup your telex account and logs channels",
    content: `Create your telex account, create specified channels to receive different activity feedbacks from your Go Applications`,
  },
  {
    id: 2,
    title: "Connect webhook url",
    content: `Integerate your webhook url into your Go applications by making a get request from your different parts of your Go application to the Webhook url`,
  },
  {
    id: 3,
    title: "Get realtime metrics",
    content: `Get alll metrics and data from different parts of your app integrated with telex in their specified channels`,
  },
];

//

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Monitor Web Applications"
        title="Optimize GoLang Web Applications with Telex Monitoring"
        content="Gain deep visibility and performance insights for your Go applications."
        routeLink=""
        routeName=""
        imageBg={ImageBg?.src}
      />
      <SecondFeatures
        heading="Full Visibility into your Go Applications"
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={data}
      />
      <SecondFeatures
        heading="Integration with GoLang:"
        content="Integrating Telex with your GoLang applications is straightforward:"
        items={seconddata}
      />
      <ImageSection image={SectionImage?.src} />
      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto py-[60px]">
          <Faq />
          <KeepTrack
            title="Monitor and optimize Google Cloud workloads"
            content="Experience the benefits firsthand and discover a more efficient way to optimize your Google Cloud."
          />
        </div>
      </div>
    </>
  );
};

export default Page;
