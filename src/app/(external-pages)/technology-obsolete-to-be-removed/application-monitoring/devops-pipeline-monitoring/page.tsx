// "use server";
import React from "react";
import <PERSON> from "../../components/application-monitoring/Hero";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import ImageBg from "../../_assets/api-monitoring-bg.svg";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import MonitorComponent from "~/app/(external-pages)/products/components/single-product/Monitor";
import Restapi from "../../_assets/restapi.svg";
import Graphql from "../../_assets/graphql.svg";
import Websocket from "../../_assets/websocket.svg";
import { APIMonitoringFaq } from "../../data/api-monitoring-faqs";

const data = [
  {
    id: 1,
    title: "Real-Time Error and Downtime Notifications",
    content: `Telex immediately notifies users when an API endpoint fails, helping individuals to act swiftly on downtime, resolve issues faster, and prevent disruptions in connected services.`,
  },
  {
    id: 2,
    title: "Latency and Performance Tracking",
    content: `Track API response times across endpoints to identify slow responses or bottlenecks. Telex provides detailed latency metrics, ensuring APIs run smoothly and efficiently, even under heavy loads.`,
  },
  {
    id: 3,
    title: "Automatic Health Checks",
    content: `Telex performs periodic health checks on all endpoints, helping developers ensure API availability and reliability. This proactive monitoring helps prevent issues before they affect users.`,
  },
  {
    id: 4,
    title: "Request Volume and Rate Limit Monitoring",
    content: `With Telex, individuals can track request volumes, understand usage patterns, and monitor API rate limits. This helps prevent overuse, identify potential abuse, and optimize resource allocation.`,
  },
  {
    id: 5,
    title: "Detailed Error Logs and Reporting",
    content: `Telex logs error details, including status codes and endpoint errors, for in-depth analysis. This enables users to diagnose issues and make necessary adjustments, enhancing overall API resilience.`,
  },
  {
    id: 6,
    title: "Dependency Mapping and Visualization",
    content: `Visualize dependencies between API services with Telex, making it easier to understand how changes or outages in one API might impact other connected systems or applications.`,
  },
];

const seconddata = [
  {
    id: 1,
    title: "Connect Your Systems",
    content: `Start by connecting your ERP, CRM, and internal systems to Telex. Our platform supports both native integrations and custom API connections to suit your specific infrastructure.`,
    link: "",
  },
  {
    id: 2,
    title: "Define Key Metrics",
    content: `Define the KPIs and performance indicators you need to monitor. From system uptime to resource usage and user interactions, Telex offers comprehensive tracking capabilities.`,
    link: "",
  },
  {
    id: 3,
    title: "Real-Time Monitoring and Alerts",
    content: `Telex immediately begins monitoring your systems, offering real-time data and sending notifications if something goes wrong. Customize alerts based on critical thresholds to ensure you're always aware of potential issues.`,
    link: "",
  },
];

const technologies = [
  {
    id: 1,
    title: "RESTful APIs",
    content:
      "Telex integrates seamlessly with REST APIs, providing complete visibility into endpoint health, status codes, and latency metrics for better management and optimization.",
    link: "",
    image: Restapi,
  },
  {
    id: 2,
    title: "GraphQL APIs",
    content:
      "With GraphQL support, Telex monitors query performance, tracks resolver behavior, and logs errors, allowing individuals to optimize GraphQL’s unique query structures and reduce latency.",
    link: "",
    image: Graphql,
  },
  {
    id: 3,
    title: "WebSocket APIs",
    content:
      "Telex supports WebSocket monitoring, tracking connection status, latency, and message throughput, ensuring real-time APIs deliver efficient and reliable service.",
    link: "",
    image: Websocket,
  },
];

//

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="APIs Monitoring"
        title="Optimize Your API Performance with Telex: Real-Time Monitoring and Intelligent Insights"
        content="Telex empowers developers to track API health, uptime, and performance with actionable insights, ensuring reliable service for users."
        routeLink=""
        routeName=""
        imageBg={ImageBg?.src}
      />

      <SecondFeatures
        heading="Get in-depth monitoring you need to ensure smooth operations"
        content="In complex environments, where multiple systems interact, it’s critical to maintain real-time visibility across every component, from infrastructure to user interactions"
        items={data}
      />

      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto pt-[80px] pb-[40px]">
          <h1 className="text-2xl md:text-3xl lg:text-[32px] font-semibold mb-5 lg:leading-snug md:w-[80%] lg:w-[50%]">
            How Telex Supports API Monitoring
          </h1>

          <div
            className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-14 mt-10`}
          >
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Improved Reliability and User Experience
              </h2>
              <p className="text-gray-500 mb-4">
                By tracking downtime and performance issues, Telex helps
                businesses deliver more reliable APIs, resulting in a better
                user experience and greater customer satisfaction.
              </p>
            </div>
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Cost Optimization Through Efficient API Usage
              </h2>
              <p className="text-gray-500 mb-4">
                With data on request volumes and resource allocation, businesses
                can optimize API usage and manage costs more effectively,
                allocating resources where they are needed most.
              </p>
            </div>
            <div className={`bg-white`}>
              <h2 className="text-lg font-medium mb-2">
                Enhanced Security and Compliance Tracking
              </h2>
              <p className="text-gray-500 mb-4">
                Telex offers built-in security monitoring, logging access
                patterns, and identifying unusual API usage. This ensures that
                businesses maintain compliance standards and reduce the risk of
                data exposure.
              </p>
            </div>
          </div>
        </div>
      </div>

      <MonitorComponent
        heading="Integration with Leading API technologies"
        items={technologies}
        showlink={false}
      />

      <MonitorComponent
        heading="How Telex Works for Enterprise Systems:"
        items={seconddata}
      />

      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto">
          <Faq faq={APIMonitoringFaq} />
          <KeepTrack
            title="Get started with Telex and experience reliable, real-time API monitoring"
            content="Start today to optimize your API performance!"
          />
        </div>
      </div>
    </>
  );
};

export default Page;
