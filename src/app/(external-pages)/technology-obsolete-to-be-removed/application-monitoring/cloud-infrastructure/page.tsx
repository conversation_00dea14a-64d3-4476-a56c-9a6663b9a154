"use server";
import React from "react";
import <PERSON> from "../../components/application-monitoring/Hero";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import ImageBg from "../../_assets/cloud-infrastructure-bg.svg";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import CloudPlatforms from "../../components/application-monitoring/CloudPlatform";
import CloudEnvironment from "../../components/application-monitoring/CloudEnvironment";

const data = [
  {
    id: 1,
    title: "Comprehensive Visualization of your cloud analytics",
    content: `Visualize your entire cloud infrastructure in a single pane of glass with real-time analytics.`,
  },
  {
    id: 2,
    title: "Usage Performance Monitoring",
    content: `You can monitor  your CPU usage, memory, and disk utilization across cloud instances to prevent downtime.`,
  },
  {
    id: 3,
    title: "Get aware early on Security breaches",
    content: `Stay on top of your security posture with instant alerts on vulnerabilities and compliance breaches.`,
  },
  {
    id: 4,
    title: "Monitor Scaling Insights",
    content: `Track autoscaling events and ensure that your infrastructure adapts to demand without wasting resources.`,
  },
  {
    id: 5,
    title: "Track your Cost & Optimization",
    content: `For Go applications using databases (e.g., PostgreSQL, MySQL), Telex can monitor database queries, query response times, and potential connection issues.`,
  },
  {
    id: 6,
    title: "Available for Cross-Platform Compatibility",
    content: `Integrate with all major cloud providers, including AWS, Microsoft Azure, Google Cloud, and hybrid clouds.`,
  },
];

//

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Cloud Infrastructure Monitoring"
        title="Take Control of Your Cloud with Telex – Comprehensive Cloud Infrastructure Monitoring"
        content="All the Features You Need to Manage Your Cloud Effectively"
        routeLink=""
        routeName=""
        imageBg={ImageBg?.src}
        width="60%"
      />
      <SecondFeatures
        heading="All the Features You Need to Manage Your Cloud Effectively"
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={data}
      />
      <CloudPlatforms />
      <CloudEnvironment />
      <div className="relative px-4 md:px-6">
        <div className="max-w-7xl mx-auto py-[60px]">
          <Faq />
          <KeepTrack
            title="Monitor and optimize Google Cloud workloads"
            content="Experience the benefits firsthand and discover a more efficient way to optimize your Google Cloud."
          />
        </div>
      </div>
    </>
  );
};

export default Page;
