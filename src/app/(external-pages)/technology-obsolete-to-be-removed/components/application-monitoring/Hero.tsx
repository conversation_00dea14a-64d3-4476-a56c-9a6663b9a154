import Link from "next/link";
import React from "react";

interface HeroProps {
  breadCumbs: string;
  title: string;
  content: string;
  routeLink: string;
  routeName: string;
  imageBg: string;
  width?: string;
}

const Hero = (props: HeroProps) => {
  //
  return (
    <div
      style={{
        backgroundImage: `url(${props?.imageBg})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundColor: "#999",
      }}
      className="flex flex-col items-center justify-center text-center py-20 md:py-[200px] bg-gray-50 px-2 md:px-6 lg:px-8"
    >
      <nav className="text-sm text-white mb-10">
        <a href="/" className="hover:underline">
          Home
        </a>{" "}
        &gt;
        <a href="/products" className="hover:underline">
          {" "}
          Technology{" "}
        </a>{" "}
        &gt; <span className="text-white">{props?.breadCumbs}</span>
      </nav>

      <h1
        className={`w-full lg:w-[${props?.width || "60%"}] text-4xl md:text-5xl font-bold text-white mb-4 lg:leading-[60px]`}
      >
        {props?.title}
      </h1>

      <p className="text-white text-base sm:text-lg mb-8 lg:w-[65%] mx-auto">
        {props?.content}
      </p>

      <div className="space-x-4">
        <Link
          href="/auth/sign-up"
          className="bg-primary-500 text-xs md:text-sm text-white font-semibold py-3 px-6 rounded-lg hover:bg-purple-700 transition duration-300 ease-in-out mb-3"
        >
          Start monitoring now
        </Link>

        <Link
          href="/pricing"
          className="bg-white text-gray-500 text-xs md:text-sm font-semibold py-3 px-6 border border-primary-500 rounded-lg hover:bg-purple-50 transition duration-300 ease-in-out mb-3"
        >
          Learn more
        </Link>
      </div>
    </div>
  );
};

export default Hero;
