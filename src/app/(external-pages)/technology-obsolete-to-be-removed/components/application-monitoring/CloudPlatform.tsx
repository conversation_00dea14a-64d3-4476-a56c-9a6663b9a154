import React from "react";
import microsoftImage from "../../_assets/microsoft.svg";
import googleCloud from "../../_assets/google-cloud.svg";
import amazon from "../../_assets/amazon.svg";
import Image from "next/image";

const CloudPlatforms = () => {
  return (
    <section className="py-12 md:py-20">
      <div className="max-w-7xl mx-auto">
        <div className="mb-10">
          <h2 className="text-2xl md:text-3xl lg:text-[32px] font-semibold mb-5 lg:leading-snug">
            Telex for all cloud platforms
          </h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* AWS Card */}
          <div className="border border-gray-300 rounded-lg p-6 shadow-sm bg-white">
            <Image
              width={120}
              height={100}
              src={amazon?.src}
              alt="AWS"
              className="-ml-4 mb-2 mt-4"
            />
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              AWS Integration
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-4">
              Telex connects to your Amazon Web Services (AWS) account to
              monitor EC2 instances, RDS databases, Lambda functions, S3
              buckets, and more. Get deep insights into your usage, performance,
              and health in real-time.
            </p>
            <ul className="list-disc list-inside text-sm sm:text-base text-gray-600 mb-4">
              <li>
                Track virtual machines (EC2), storage (S3), and networking
                services in real-time.
              </li>
              <li>
                Monitor CloudWatch metrics and set custom alerts for resource
                thresholds.
              </li>
              <li>
                Keep tabs on auto-scaling, load balancing, and Elastic Beanstalk
                deployments.
              </li>
            </ul>
            <a
              href="#"
              className="text-primary-500 font-semibold text-sm sm:text-base hover:underline"
            >
              Learn More
            </a>
          </div>

          {/* Google Cloud Card */}
          <div className="border border-gray-300 rounded-lg p-6 shadow-sm bg-white">
            <Image
              width={200}
              height={100}
              src={googleCloud?.src}
              alt="Google Cloud"
            />
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Google Cloud Platform Integration
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-4">
              Telex monitors Google Cloud Platform services, from Compute Engine
              and Cloud SQL to Kubernetes clusters. Get a comprehensive overview
              of your GCP usage, set performance alerts, and identify
              optimization opportunities.
            </p>
            <ul className="list-disc list-inside text-sm sm:text-base text-gray-600 mb-4">
              <li>
                Monitor Google Cloud Compute Engine, Cloud SQL, and Kubernetes
                Engine.
              </li>
              <li>
                Track performance, manage costs, and detect issues easily with
                real-time alerts.
              </li>
              <li>
                View detailed metrics on storage, load balancers, and service
                health.
              </li>
            </ul>
            <a
              href="#"
              className="text-primary-500 font-semibold text-sm sm:text-base hover:underline"
            >
              Learn More
            </a>
          </div>

          {/* Microsoft Azure Card */}
          <div className="border border-gray-300 rounded-lg p-6 shadow-sm bg-white">
            <Image
              width={200}
              height={100}
              src={microsoftImage?.src}
              alt="Microsoft Azure"
            />
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Microsoft Azure
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-4">
              Monitor all your Microsoft Azure resources, including VMs, app
              services, and SQL databases. Telex integrates with Azure Monitor
              to provide detailed analytics and alerts on the health of your
              cloud infrastructure.
            </p>
            <ul className="list-disc list-inside text-sm sm:text-base text-gray-600 mb-4">
              <li>Track virtual machines, storage, and databases on Azure.</li>
              <li>
                Automate alerts for performance issues and downtime risks.
              </li>
              <li>
                Monitor Azure-specific services like App Services, Blob Storage,
                and more.
              </li>
            </ul>
            <a
              href="#"
              className="text-primary-500 font-semibold text-sm sm:text-base hover:underline"
            >
              Learn More
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CloudPlatforms;
