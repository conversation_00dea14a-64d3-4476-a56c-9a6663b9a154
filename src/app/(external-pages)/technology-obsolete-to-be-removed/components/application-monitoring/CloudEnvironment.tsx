import React from "react";
import environment from "../../_assets/environment.svg";
import ImageSection from "./ImageSection";

const CloudEnvironment = () => {
  return (
    <section className="py-12 md:py-16">
      <div className="max-w-7xl mx-auto">
        <div className="mb-20">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-5 w-full lg:w-[70%]">
            Telex with cloud environments for effective monitoring
          </h2>

          <p className="text-sm sm:text-base lg:text-lg text-gray-600">
            Integrating Telex with your GoLang applications is straightforward:
          </p>
        </div>

        <ImageSection image={environment?.src} />

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 mt-14">
          {/* API Integration */}
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              API Integration
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-2">
              <strong>How it works:</strong> Telex can connect to your cloud
              provider via their APIs. Cloud platforms like AWS, Azure, and GCP
              provide API endpoints that allow Telex to pull metrics, logs, and
              data about your infrastructure.
            </p>
            <p className="text-sm sm:text-base text-gray-600">
              <strong>Benefits:</strong> Flexible, scalable, and can be
              customized to monitor specific cloud services or instances.
            </p>
          </div>

          {/* Agent-Based Monitoring */}
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Agent-Based Monitoring
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-2">
              <strong>How it works:</strong> Telex can deploy lightweight agents
              on cloud-based virtual machines (VMs) or containers to gather
              performance metrics, resource usage, and logs directly from the
              instance.
            </p>
            <p className="text-sm sm:text-base text-gray-600">
              <strong>Benefits:</strong> Real-time insights into individual
              instances with detailed CPU, memory, disk, and network metrics.
            </p>
          </div>

          {/* Cloudwatch Integration */}
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Cloudwatch (AWS) / Azure Monitor / GCP Monitoring Integration
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-2">
              <strong>How it works:</strong> Telex integrates directly with
              native cloud monitoring services like AWS CloudWatch, Azure
              Monitor, or Google Cloud Operations Suite.
            </p>
            <p className="text-sm sm:text-base text-gray-600">
              <strong>Benefits:</strong>{" "}
              {`Seamless integration with your cloud's`}
              native monitoring tools, simplifying data collection and alerting.
            </p>
          </div>

          {/* Log Collection */}
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Log Collection and Analysis
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-2">
              <strong>How it works:</strong> Connect Telex to cloud log storage
              services such as AWS CloudTrail, Azure Log Analytics, or GCP Cloud
              Logging.
            </p>
            <p className="text-sm sm:text-base text-gray-600">
              <strong>Benefits:</strong> Enhanced security and operational
              visibility with detailed log insights.
            </p>
          </div>

          {/* Webhook Integration */}
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Webhook Integration
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-2">
              <strong>How it works:</strong> Set up webhooks in your cloud
              environment to send event data (like scaling, errors, or outages)
              to Telex for real-time notifications.
            </p>
            <p className="text-sm sm:text-base text-gray-600">
              <strong>Benefits:</strong> Immediate notification of cloud events
              or incidents, enabling faster response times.
            </p>
          </div>

          {/* Third-Party Integration */}
          <div>
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Third-Party Integration Tools
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-2">
              <strong>How it works:</strong> Use tools like Terraform,
              Kubernetes, or Docker Swarm to automatically send infrastructure
              status updates and performance data to Telex.
            </p>
            <p className="text-sm sm:text-base text-gray-600">
              <strong>Benefits:</strong> Automated scaling and health monitoring
              of complex, dynamic cloud setups.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CloudEnvironment;
