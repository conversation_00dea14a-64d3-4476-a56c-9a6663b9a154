"use client";
import React from "react";
import Image from "next/image";

//

interface Props {
  image: string;
}

const ImageSection = (props: Props) => {
  const preventAction = (e: any) => {
    e.preventDefault();
  };

  return (
    <div className="max-w-7xl mx-auto">
      <Image
        src={props?.image}
        alt=""
        className="h-full object-cover h-full w-full mx-auto"
        width={100}
        height={100}
        onContextMenu={preventAction}
      />
    </div>
  );
};

export default ImageSection;
