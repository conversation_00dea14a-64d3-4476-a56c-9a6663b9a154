import React from "react";
import { ArrowRight, LibraryBig } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const TechnologyArticle = () => {
  const techArticles = [
    {
      title: "Application Performance Monitoring",
      count: 1,
      articles: [{ title: "Speed Up Node.js Apps", link: "/technology" }],
    },
    {
      title: "Cloud Monitoring",
      count: 2,
      articles: [
        { title: "Monitor AWS EC2 Health in Real-Time", link: "/technology" },
        {
          title: "Track Google Cloud Uptime Effortlessly",
          link: "/technology",
        },
      ],
    },
    {
      title: "Database Monitoring",
      count: 2,
      articles: [
        {
          title: "Monitor MySQL Uptime Without Manual Checks",
          link: "/technology/monitor-mysql-uptime",
        },
        { title: "Catch MongoDB Downtime Before Clients", link: "/technology" },
      ],
    },
    {
      title: "Log Analysis",
      count: 1,
      articles: [
        {
          title: "Analyze Logs in Kubernetes Clusters at Scale",
          link: "/technology",
        },
      ],
    },
    {
      title: "Webhook Testing",
      count: 2,
      articles: [
        { title: "Test Stripe Webhooks in Real-Time", link: "/technology" },
        { title: "Ensure GitHub Webhooks Fire Correctly", link: "/technology" },
      ],
    },
    {
      title: "Website Testing",
      count: 1,
      articles: [
        { title: "Test Website Uptime Across Regions", link: "/technology" },
      ],
    },
  ];

  return (
    <main className="flex justify-center w-full max-w-3xl m-auto pt-8 md:pt-[96px] md:pb-[50px] pb-8">
      <div className="w-full">
        <div className="flex justify-center">
          <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
            <LibraryBig className="w-4 h-4" />
            <span>Articles</span>
          </div>
        </div>

        <div className="text-center mb-10">
          <h2 className="text-[#101828] font-semibold sm:text-4xl text-3xl leading-10">
            Use-Cases That Work, Because Your Time Matters
          </h2>
          <p className="text-[#344054] text-base font-normal leading-6 mt-2 max-w-lg mx-auto">
            Wondering what to use Telex for? Here are practical uses for Telex
            in your everyday work.
          </p>
        </div>

        <div>
          {techArticles.map((section, index) => (
            <div
              key={index}
              className="w-full border border-[#E4E7EC] rounded-[10px] flex flex-col mb-8"
            >
              <div className="flex items-center gap-[10px] sm:p-5 p-3 border-b border-[#E4E7EC] flex-wrap">
                <h3 className="text-[#101828] font-semibold sm:text-lg text-base leading-7">
                  {section.title}
                </h3>
                <Image
                  src="/tech-dot.svg"
                  alt="dot on tech articles"
                  width={8}
                  height={8}
                />
                <p className="text-[#475467] sm:text-sm text-xs font-medium leading-5">
                  {section.count} article{section.count > 1 && "s"}
                </p>
              </div>

              <div className="p-1">
                {section.articles.map((article, i) => (
                  <Link key={i} href={article.link} className="block w-full">
                    <div className="w-full flex items-center gap-3 justify-between sm:px-5 px-3 sm:py-[14px] py-3 hover:bg-[#F1F1FE] transition-colors rounded-[10px]">
                      <p className="text-[#101828] text-base leading-normal tracking-[0.16px]">
                        {article.title}
                      </p>
                      <ArrowRight className="text-[#344054] w-4 h-4 cursor-pointer" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </main>
  );
};

export default TechnologyArticle;
