import React from "react";

//
interface dataObject {
  id: number;
  title: string;
  content: string;
}

interface Props {
  heading: string;
  items: dataObject[];
  content: string;
}

const SecondFeatures = (props: Props) => {
  return (
    <div className="relative px-4 md:px-6">
      <div className="max-w-7xl mx-auto pt-[80px] pb-[40px]">
        <h1 className="text-2xl md:text-3xl lg:text-[32px] font-bold mb-5 lg:leading-snug md:w-[80%] lg:w-[50%]">
          {props?.heading}
        </h1>
        <p className="text-gray-500 mb-10">{props?.content}</p>

        <div className={`grid grid-cols-1 sm:grid-cols-2 gap-6 lg:grid-cols-3`}>
          {props?.items?.map((item, index: number) => {
            return (
              <div key={index} className={`bg-white`}>
                <h2 className="text-lg font-semibold mb-2">{item.title}</h2>
                <p className="text-gray-500 mb-4">{item?.content}</p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default SecondFeatures;
