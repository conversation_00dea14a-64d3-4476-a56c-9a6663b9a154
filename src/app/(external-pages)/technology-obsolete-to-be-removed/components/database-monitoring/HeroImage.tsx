"use client";
import React from "react";
import Image from "next/image";

interface HeroProps {
  imageBg: string;
}

const HeroImage = (props: HeroProps) => {
  const preventAction = (e: any) => {
    e.preventDefault();
  };

  //
  return (
    <div className="mt-10 bg-gray-100">
      <Image
        src={props?.imageBg}
        alt=""
        width={100}
        height={100}
        className="w-full"
        onContextMenu={preventAction}
      />
    </div>
  );
};

export default HeroImage;
