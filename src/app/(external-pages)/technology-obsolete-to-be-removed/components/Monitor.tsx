"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation, Scrollbar } from "swiper/modules";
import Image from "next/image";
import Link from "next/link";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import "swiper/css/scrollbar";

interface dataObject {
  id: number;
  title: string;
  content: string;
  link: string;
  image?: string;
}

interface Props {
  heading: string;
  items: dataObject[];
}

const MonitorComponent = (props: Props) => {
  return (
    <div className="relative px-4 md:px-6">
      <div className="max-w-7xl mx-auto py-[60px] overflow-hidden">
        <h1 className="text-2xl md:text-3xl lg:text-[32px] font-semibold mb-10 lg:leading-snug md:w-[80%] lg:w-[50%]">
          {props?.heading}
        </h1>

        <Swiper
          modules={[Pagination, Navigation, Scrollbar]}
          spaceBetween={20}
          slidesPerView={1}
          // navigation
          pagination={{ clickable: true }}
          breakpoints={{
            640: {
              slidesPerView: 1.5,
              spaceBetween: 20,
            },
            768: {
              slidesPerView: 2.5,
              spaceBetween: 30,
            },
            1024: {
              slidesPerView: 2.5,
              spaceBetween: 30,
            },
            1440: {
              slidesPerView: 3.5,
              spaceBetween: 40,
            },
          }}
          className="w-full"
        >
          {props?.items?.map((item, index) => (
            <SwiperSlide key={index}>
              <div className="py-6 bg-white rounded-lg mb-10">
                {item?.image && (
                  <Image
                    src={item.image || ""}
                    alt={item.title}
                    width={
                      item?.title.includes("Microsoft SQL Server Monitoring") ||
                      item?.title.includes("Redis Monitoring")
                        ? 150
                        : 80
                    }
                    height={80}
                    className="mb-3"
                  />
                )}
                <h2 className="text-lg font-medium mb-2 xl:w-[70%]">
                  {item.title}
                </h2>
                <p className="text-gray-500 mb-4">{item?.content}</p>
                <Link
                  href={item.link}
                  className="text-blue-600 font-medium hover:underline"
                >
                  Learn More
                </Link>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      <style jsx global>{`
        .swiper-pagination {
          text-align: left;
          width: 200px !important;
        }
        .swiper-pagination-bullet {
          width: 12px;
          height: 4px;
          background: grey;
          border-radius: 20px;
          margin: 40px 8px 0 0 !important;
        }

        .swiper-pagination-bullet-active {
          background: #8b5cf6;
          width: 35px;
        }
      `}</style>
    </div>
  );
};

export default MonitorComponent;
