import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import <PERSON> from "../../components/database-monitoring/Hero";
import ImageBg from "../../_assets/redis-hero.svg";
import AllFeatures from "../../components/database-monitoring/AllFeatures";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import { redisFaq } from "../../data/redis-faqs";

const firstData = [
  {
    id: 1,
    title: "Latency Tracking",
    content: `Monitor latency to maintain efficient data caching.`,
  },
  {
    id: 2,
    title: "Memory Usage Alerts",
    content: `Get notified of memory thresholds to avoid cache evictions.`,
  },
  {
    id: 3,
    title: "Command Execution Insights",
    content: `Track slow commands that may impact system speed`,
  },
  {
    id: 4,
    title: "Keyspace Tracking",
    content: `Monitor the number of keys, evictions, and expiration rates.`,
  },
];

const secondData = [
  {
    id: 1,
    title: "Real-Time Connection Health Checks",
    content: `Tel<PERSON> continuously monitors the database's connectivity, alerting users instantly if the server goes down or if there are connection latency issues. This ensures databases are always accessible, and downtime is minimized.`,
  },
  {
    id: 2,
    title: "Query Performance Insights",
    content: `Telex tracks the execution times of queries, helping users identify any slow-running or resource-intensive queries. By pinpointing bottlenecks, users can optimize query performance, which is crucial for databases like MySQL, PostgreSQL, and SQL Server.`,
  },
  {
    id: 3,
    title: "Resource Utilization Monitoring",
    content: `Telex provides real-time metrics on CPU, memory, and disk usage within databases, including MongoDB and Redis. Users receive alerts if resource consumption crosses set thresholds, allowing proactive adjustments to avoid overloads.`,
  },
];

const Redis = () => {
  return (
    <>
      <Hero
        breadCumbs="Redis Monitoring"
        title="Maximize Redis Performance with Telex Real-Time Monitoring."
        content="Gain real-time visibility into your Redis environment to manage cache performance and stability with Telex."
        routeName="Start free trial"
        routeLink="#"
        imageBg={ImageBg}
      />
      <AllFeatures
        heading="All the Features You Need to Manage Your Redis Databases"
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={firstData}
      />
      <SecondFeatures
        heading="How Telex Works for Redis Databases"
        content="Integrating Telex with Databases can provide:"
        items={secondData}
      />
      <Faq faq={redisFaq} />
      <KeepTrack
        title="Don’t let Redis slowdowns impact your system."
        content="Ensure operational continuity, user experience, and security by leveraging Telex’s intelligent monitoring solutions. Get started with a free demo tailored to your needs."
      />
    </>
  );
};

export default Redis;
