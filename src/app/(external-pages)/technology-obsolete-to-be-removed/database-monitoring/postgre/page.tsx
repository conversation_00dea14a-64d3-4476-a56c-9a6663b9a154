import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import <PERSON> from "../../components/database-monitoring/Hero";
import ImageBg from "../../_assets/postgre-hero.svg";
import AllFeatures from "../../components/database-monitoring/AllFeatures";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import { postgreFaq } from "../../data/postgre-faqs";

const firstData = [
  {
    id: 1,
    title: "Performance Metrics",
    content: `Monitor query execution times, load balance, and indexing efficiency.`,
  },
  {
    id: 2,
    title: "Resource Usage",
    content: `Get alerts on memory, CPU, and storage consumption to prevent server overloads.`,
  },
  {
    id: 3,
    title: "Security and Access Logs",
    content: `Track unauthorized access attempts and security threats.`,
  },
  {
    id: 4,
    title: "Backup and Restore Checks",
    content: `Ensure backups are completed on schedule to protect against data loss.`,
  },
];
const secondData = [
  {
    id: 1,
    title: "Real-Time Connection Health Checks",
    content: `Telex continuously monitors the database's connectivity, alerting users instantly if the server goes down or if there are connection latency issues. This ensures databases are always accessible, and downtime is minimized.`,
  },
  {
    id: 2,
    title: "Query Performance Insights",
    content: `Telex tracks the execution times of queries, helping users identify any slow-running or resource-intensive queries. By pinpointing bottlenecks, users can optimize query performance, which is crucial for databases like MySQL, PostgreSQL, and SQL Server.`,
  },
  {
    id: 3,
    title: "Resource Utilization Monitoring",
    content: `Telex provides real-time metrics on CPU, memory, and disk usage within databases, including MongoDB and Redis. Users receive alerts if resource consumption crosses set thresholds, allowing proactive adjustments to avoid overloads.`,
  },
];

const PostGre = () => {
  return (
    <>
      <Hero
        breadCumbs="PostgreSQL Database Monitoring"
        title="Ensure PostgreSQL Stability with Telex’s Real-Time Monitoring."
        content="Telex helps you detect issues early in PostgreSQL databases, so you can maintain efficiency and performance."
        routeName="Start free trial"
        routeLink="#"
        imageBg={ImageBg}
      />
      <AllFeatures
        heading="All the Features Telex Can Offer for PostgreSQL Monitoring"
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={firstData}
      />
      <SecondFeatures
        heading="How Telex Works for PostgreSQL Databases"
        content="Integrating Telex with Databases can provide:"
        items={secondData}
      />
      <Faq faq={postgreFaq} />
      <KeepTrack
        title="Get peace of mind with PostgreSQL monitoring."
        content="Sign up with Telex for a free trial today!"
      />
    </>
  );
};

export default PostGre;
