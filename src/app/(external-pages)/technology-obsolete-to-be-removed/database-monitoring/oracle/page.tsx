import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import <PERSON> from "../../components/database-monitoring/Hero";
import ImageBg from "../../_assets/oracle-hero.svg";
import AllFeatures from "../../components/database-monitoring/AllFeatures";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import { oracleFaq } from "../../data/oracle-faqs";

const firstData = [
  {
    id: 1,
    title: "Session and Transaction Monitoring",
    content: `Keep track of active sessions to maintain performance.`,
  },
  {
    id: 2,
    title: "Wait Times and Bottlenecks",
    content: `Identify bottlenecks that delay queries.`,
  },
  {
    id: 3,
    title: "Resource Usage",
    content: `Monitor CPU, memory, and storage to prevent downtime.`,
  },
  {
    id: 4,
    title: "Backup and Restore Tracking",
    content: `Ensure backups complete on time to avoid data loss.`,
  },
];

const secondData = [
  {
    id: 1,
    title: "Real-Time Connection Health Checks",
    content: `Tel<PERSON> continuously monitors the database's connectivity, alerting users instantly if the server goes down or if there are connection latency issues. This ensures databases are always accessible, and downtime is minimized.`,
  },
  {
    id: 2,
    title: "Query Performance Insights",
    content: `Telex tracks the execution times of queries, helping users identify any slow-running or resource-intensive queries. By pinpointing bottlenecks, users can optimize query performance, which is crucial for databases like MySQL, PostgreSQL, and SQL Server.`,
  },
  {
    id: 3,
    title: "Resource Utilization Monitoring",
    content: `Telex provides real-time metrics on CPU, memory, and disk usage within databases, including MongoDB and Redis. Users receive alerts if resource consumption crosses set thresholds, allowing proactive adjustments to avoid overloads.`,
  },
];

const Oracle = () => {
  return (
    <>
      <Hero
        breadCumbs="Monitor Oracle Database"
        title="Oracle Database Monitoring Made Easy with Telex."
        content="Telex provides real-time insights into Oracle database health and performance, helping you stay on top of critical operations."
        routeName="Start free trial"
        routeLink="#"
        imageBg={ImageBg}
      />
      <AllFeatures
        heading="All the Features You Need to Manage Your Oracle Database"
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={firstData}
      />
      <SecondFeatures
        heading="How Telex Works for Oracle Databases"
        content="Integrating Telex with Databases can provide:"
        items={secondData}
      />
      <Faq faq={oracleFaq} />
      <KeepTrack
        title="Protect your Oracle databases with Telex."
        content="Ensure operational continuity, user experience, and security by leveraging Telex’s intelligent monitoring solutions. Get started with a free demo tailored to your needs."
      />
    </>
  );
};

export default Oracle;
