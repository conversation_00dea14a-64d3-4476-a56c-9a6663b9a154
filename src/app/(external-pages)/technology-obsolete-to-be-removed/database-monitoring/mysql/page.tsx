import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
// import CaseOne from "../_assets/database1.svg";
// import CaseTwo from "../_assets/database2.svg";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import <PERSON> from "../../components/database-monitoring/Hero";
import ImageBg from "../../_assets/mysql-hero.svg";
import AllFeatures from "../../components/database-monitoring/AllFeatures";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import { mysqlFaq } from "../../data/mysql-faqs";

const firstData = [
  {
    id: 1,
    title: "Connection Health Tracking",
    content: `Monitor active connections and their response times to keep your database accessible and optimized.`,
  },
  {
    id: 2,
    title: "Query Performance Analysis",
    content: `Identify slow-running queries and detect bottlenecks affecting performance.`,
  },
  {
    id: 3,
    title: "Memory and Storage Monitoring",
    content: `Track resource usage to prevent outages due to memory or storage limitations.`,
  },
  {
    id: 4,
    title: "Error Alerts",
    content: `Receive notifications on critical errors for fast troubleshooting and resolution.`,
  },
];
const secondData = [
  {
    id: 1,
    title: "Real-Time Connection Health Checks",
    content: `Telex continuously monitors the database's connectivity, alerting users instantly if the server goes down or if there are connection latency issues. This ensures databases are always accessible, and downtime is minimized.`,
  },
  {
    id: 2,
    title: "Query Performance Insights",
    content: `Telex tracks the execution times of queries, helping users identify any slow-running or resource-intensive queries. By pinpointing bottlenecks, users can optimize query performance, which is crucial for databases like MySQL, PostgreSQL, and SQL Server.`,
  },
  {
    id: 3,
    title: "Resource Utilization Monitoring",
    content: `Telex provides real-time metrics on CPU, memory, and disk usage within databases, including MongoDB and Redis. Users receive alerts if resource consumption crosses set thresholds, allowing proactive adjustments to avoid overloads.`,
  },
];

const Mysql = () => {
  return (
    <>
      <Hero
        breadCumbs="Monitor MySQL Databases"
        title="Real-Time MySQL Monitoring with Telex: Stay Informed, Act Swiftly."
        content="Gain powerful insights into your MySQL database with Telex’s real-time alert system, enabling fast responses to performance and availability issues."
        routeName="Start free trial"
        routeLink="#"
        imageBg={ImageBg}
      />
      <AllFeatures
        heading="All the Features You Need to Manage Your SQL Databases."
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={firstData}
      />
      <SecondFeatures
        heading="How Telex Works for SQL Databases."
        content="Integrating Telex with Databases can provide:"
        items={secondData}
      />
      <Faq faq={mysqlFaq} />
      <KeepTrack
        title="Experience seamless MySQL monitoring today"
        content="Start your free trial with Telex and keep your database running optimally."
      />
    </>
  );
};

export default Mysql;
