import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import <PERSON> from "../../components/database-monitoring/Hero";
import ImageBg from "../../_assets/sqlserver-hero.svg";
import AllFeatures from "../../components/database-monitoring/AllFeatures";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import { sqlserverFaq } from "../../data/sqlserver-faqs";

const firstData = [
  {
    id: 1,
    title: "Query Response Time",
    content: `Monitor long-running queries to optimize performance.`,
  },
  {
    id: 2,
    title: "Transaction Logs",
    content: `Track log status and prevent data loss from incomplete transactions.`,
  },
  {
    id: 3,
    title: "CPU and Memory",
    content: `Stay informed on CPU, memory, and storage metrics for smooth operation.`,
  },
  {
    id: 4,
    title: "Index Usage",
    content: `Evaluate index usage and efficiency for faster data access.`,
  },
];

const secondData = [
  {
    id: 1,
    title: "Real-Time Connection Health Checks",
    content: `Telex continuously monitors the database's connectivity, alerting users instantly if the server goes down or if there are connection latency issues. This ensures databases are always accessible, and downtime is minimized.`,
  },
  {
    id: 2,
    title: "Query Performance Insights",
    content: `Telex tracks the execution times of queries, helping users identify any slow-running or resource-intensive queries. By pinpointing bottlenecks, users can optimize query performance, which is crucial for databases like MySQL, PostgreSQL, and SQL Server.`,
  },
  {
    id: 3,
    title: "Resource Utilization Monitoring",
    content: `Telex provides real-time metrics on CPU, memory, and disk usage within databases, including MongoDB and Redis. Users receive alerts if resource consumption crosses set thresholds, allowing proactive adjustments to avoid overloads.`,
  },
];

const MicrosoftSqlServer = () => {
  return (
    <>
      <Hero
        breadCumbs="Microsoft SQL Server Monitoring"
        title="Ensure SQL Server Performance with Real-Time Telex Monitoring."
        content="Telex offers real-time SQL Server monitoring to detect performance issues and maintain data availability."
        routeName="Start free trial"
        routeLink="#"
        imageBg={ImageBg}
      />
      <AllFeatures
        heading="All the Features You Need to Manage Your SQL Server"
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={firstData}
      />
      <SecondFeatures
        heading="How Telex Works for SQL Server Databases"
        content="Integrating Telex with Databases can provide:"
        items={secondData}
      />
      <Faq faq={sqlserverFaq} />
      <KeepTrack
        title="Optimize SQL Server performance with Telex today"
        content="Ensure operational continuity, user experience, and security by leveraging Telex’s intelligent monitoring solutions. Get started with a free demo tailored to your needs."
      />
    </>
  );
};

export default MicrosoftSqlServer;
