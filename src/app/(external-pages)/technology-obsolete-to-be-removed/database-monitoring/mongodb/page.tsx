import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import KeepTrack from "~/app/(external-pages)/products/components/single-product/KeepTrack";
import <PERSON> from "../../components/database-monitoring/Hero";
import ImageBg from "../../_assets/mongodb-hero.svg";
import AllFeatures from "../../components/database-monitoring/AllFeatures";
import SecondFeatures from "../../components/database-monitoring/SecondFeatures";
import { mongodbFaq } from "../../data/mongodb-faqs";

const firstData = [
  {
    id: 1,
    title: "Replica Set Monitoring",
    content: `Ensure replication works correctly for high availability.`,
  },
  {
    id: 2,
    title: "Query Optimization",
    content: `Detect slow or blocked queries to improve application responsiveness.`,
  },
  {
    id: 3,
    title: "Disk and Memory Usage",
    content: `Track storage, memory, and CPU to avoid performance degradation.`,
  },
  {
    id: 4,
    title: "Index Performance",
    content: `Monitor index usage and efficiency for optimal data retrieval speeds.`,
  },
];
const secondData = [
  {
    id: 1,
    title: "Real-Time Connection Health Checks",
    content: `Telex continuously monitors the database's connectivity, alerting users instantly if the server goes down or if there are connection latency issues. This ensures databases are always accessible, and downtime is minimized.`,
  },
  {
    id: 2,
    title: "Query Performance Insights",
    content: `Telex tracks the execution times of queries, helping users identify any slow-running or resource-intensive queries. By pinpointing bottlenecks, users can optimize query performance, which is crucial for databases like MySQL, PostgreSQL, and SQL Server.`,
  },
  {
    id: 3,
    title: "Resource Utilization Monitoring",
    content: `Telex provides real-time metrics on CPU, memory, and disk usage within databases, including MongoDB and Redis. Users receive alerts if resource consumption crosses set thresholds, allowing proactive adjustments to avoid overloads.`,
  },
];

const MongoDb = () => {
  return (
    <>
      <Hero
        breadCumbs="Monotor MongoDB Databases"
        title="Seamless MongoDB Monitoring with Real-Time Insights from Telex."
        content="Telex empowers you with detailed, real-time analytics for your MongoDB environment to stay ahead of issues."
        routeName="Start free trial"
        routeLink="#"
        imageBg={ImageBg}
      />
      <AllFeatures
        heading="All the Features You Need to Manage Your MongoDB Databases"
        content="Telex provides comprehensive monitoring solutions for GoLang web applications, enabling developers to gain deep visibility into application performance and system health. In today's fast-paced development environments, observability is crucial for maintaining high performance and user satisfaction."
        items={firstData}
      />
      <SecondFeatures
        heading="How Telex Works for MongoDB Databases"
        content="Integrating Telex with Databases can provide:"
        items={secondData}
      />
      <Faq faq={mongodbFaq} />
      <KeepTrack
        title="Start optimizing your MongoDB database with Telex."
        content="Ensure operational continuity, user experience, and security by leveraging Telex’s intelligent monitoring solutions. Get started with a free demo tailored to your needs."
      />
    </>
  );
};

export default MongoDb;
