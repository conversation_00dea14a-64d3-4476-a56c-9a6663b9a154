import React from "react";
import Faq from "~/telexComponents/homePageFaqs";
import AutopilotCTA from "~/telexComponents/autopilotCTA";
import TechnologyArticle from "./components/articles";
import { Button } from "~/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export default function Technology() {
  return (
    <>
      <div className="px-6 py-[50px]">
        <div className="max-w-7xl mx-auto">
          <div className="max-w-5xl mx-auto space-y-5 mt-6 px-6">
            <h1 className="lg:text-5xl md:text-4xl text-3xl font-semibold text-center mx-auto leading-normal md:leading-snug lg:leading-snug">
              Real-World Use Cases:
              <span className="text-primary-500">
                {" "}
                How Telex Solves Technical Challenges
              </span>
            </h1>

            <p className="text-muted-foreground text-center max-w-3xl mx-auto lg:text-lg">
              Whatever your stack, wherever your bottleneck—Telex’s AI agents
              integrate seamlessly to monitor, analyze, and optimize your
              systems. Explore practical solutions tailored for your technology.
            </p>

            <div className="flex justify-center items-center gap-4 flex-col [@media(min-width:425px)]:flex-row">
              <Link href="/auth/sign-up">
                <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] hover:bg-opacity-80 text-white font-medium px-8 py-6">
                  Start Monitoring
                </Button>
              </Link>

              <Link href="/agents">
                <Button className="bg-white hover:bg-gray-100 cursor-pointer border border-primary-500 text-primary-500 font-medium px-8 py-6">
                  Meet The Agents
                </Button>
              </Link>
            </div>
          </div>

          <div className="bg-[#FAFAFF] md:mt-12 mt-6 rounded-[20px] border border-[#E6EAEF] md:p-6 p-4 sm:gap-y-6 gap-y-4 gap-x-1 flex flex-wrap [@media(max-width:1050px)]:gap-x-6">
            <div className="w-[280px] [@media(max-width:704px)]:w-full">
              <Image
                src="/taylor-tech.png"
                alt="karen franklin"
                width={282}
                height={278}
                className="w-full h-full sm:h-auto object-contain rounded-[14px]"
              />
            </div>

            <Image
              src="/gap-box.svg"
              alt="gap-box"
              width={24}
              height={20}
              className="[@media(max-width:1050px)]:hidden"
            />

            <div className="bg-white rounded-[10px] xs:px-4 px-2 py-5 w-full xs:flex-1 xs:min-w-[300px]">
              <div className="bg-[#FEE8E6] px-4 py-1 flex items-center justify-center w-fit rounded-[10px]">
                <p className="text-[#F81404] text-xs leading-4 font-medium">
                  Pain Points
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-4">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Spending 4-5 hours/day monitoring uptime, jumping between
                  dashboards
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Scrambling when customers flag downtime before her team does
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Manual, late-night server checks
                </p>
              </div>

              <div className="flex items-center gap-2 mt-3">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Struggles to report to stakeholders quickly with reliable
                  data.
                </p>
              </div>
            </div>

            <Image
              src="/gap-arrow.svg"
              alt="gap-arrow"
              width={30}
              height={40}
              className="[@media(max-width:1050px)]:hidden"
            />

            <div className="bg-white rounded-[10px] xs:px-4 px-2 py-5 w-full xs:flex-1 xs:min-w-[300px]">
              <div className="bg-[#E6FAEF] px-4 py-1 flex items-center justify-center w-fit rounded-[10px]">
                <p className="text-[#005C2B] text-xs leading-4 font-medium">
                  Telex Solution
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-4">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Monitor MySQL uptime without staying up late
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Track cloud server downtime in real-time
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Get instant alerts when your SSL certificate expires
                </p>
              </div>

              <div className="flex items-center gap-2 mt-3">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Historical data instantly accessible, making stakeholder
                  reporting painless.
                </p>
              </div>
            </div>

            <div className="bg-white rounded-[10px] xs:px-4 px-2 py-5 w-full xs:flex-1 xs:min-w-[300px]">
              <div className="bg-[#E6FAEF] px-4 py-1 flex items-center justify-center w-fit rounded-[10px]">
                <p className="text-[#005C2B] text-xs leading-4 font-medium">
                  Telex Solution
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-4">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Speed Up Your E-Commerce Checkout with Real-Time Page
                  Monitoring
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Catch Broken Links Before Your Customers Do
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Monitor Database Response Time Effortlessly
                </p>
              </div>

              <div className="flex items-center gap-2 mt-4">
                <Image
                  src="/check-tech.svg"
                  alt="check"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Create a team channel or convert your group chat into a
                  channel with ease
                </p>
              </div>
            </div>

            <Image
              src="/gap-arrow.svg"
              alt="gap-arrow"
              width={30}
              height={40}
              className="-scale-x-100 [@media(max-width:1050px)]:hidden"
            />

            <div className="bg-white rounded-[10px] xs:px-4 px-2 py-5 w-full xs:flex-1 xs:min-w-[300px]">
              <div className="bg-[#FEE8E6] px-4 py-1 flex items-center justify-center w-fit rounded-[10px]">
                <p className="text-[#F81404] text-xs leading-4 font-medium">
                  Pain Points
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-4">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Manually checking page speed, SSL, broken links 3+ hours/day
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Constant coordination between Dev & Ops teams
                </p>
              </div>

              <div className="flex items-center gap-2 pb-3 border-b border-[#F2F4F7] mt-3">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Catching bugs after customers complain
                </p>
              </div>

              <div className="flex items-center gap-2 mt-3">
                <Image
                  src="/cancel-tech.svg"
                  alt="cancel"
                  width={16}
                  height={16}
                />

                <p className="text-black font-normal text-sm leading-5">
                  Lack of a unified team view—causing fragmented responses.
                </p>
              </div>
            </div>

            <Image
              src="/gap-box.svg"
              alt="gap-box"
              width={24}
              height={20}
              className="[@media(max-width:1050px)]:hidden"
            />

            <div className="w-[280px] [@media(max-width:704px)]:w-full">
              <Image
                src="/karen-tech.png"
                alt="karen franklin"
                width={282}
                height={278}
                className="w-full h-full sm:h-auto object-contain rounded-[14px]"
              />
            </div>
          </div>
        </div>
        <TechnologyArticle />
        <Faq />
      </div>
      <AutopilotCTA />
    </>
  );
}
