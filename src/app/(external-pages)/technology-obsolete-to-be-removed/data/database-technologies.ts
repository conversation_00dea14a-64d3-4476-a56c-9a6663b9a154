import mysql from "../_assets/mysql.svg";
import postgre from "../_assets/postgre.svg";
import mongodb from "../_assets/mongodb.svg";
import redis from "../_assets/redis.svg";

// technologies
export const databaseTechnologies = [
  {
    id: 1,
    title: "Monitor MySQL Databases",
    content:
      "Telex can track query performance, replication, CPU usage, and connection health to detect slowdowns and bottlenecks.",
    link: "/technology/database-monitoring/mysql",
    image: mysql,
  },
  {
    id: 2,
    title: "PostgreSQL Database Monitoring",
    content:
      "Telex can monitor query times, replication status, memory usage, and deadlocks to ensure database efficiency.",
    link: "/technology/database-monitoring/postgre",
    image: postgre,
  },
  {
    id: 3,
    title: "Monitor MongoDB Database ",
    content:
      "Telex can check read/write performance, memory usage, and index efficiency to optimize NoSQL operations.",
    link: "/technology/database-monitoring/mongodb",
    image: mongodb,
  },
  {
    id: 4,
    title: "Redis Monitoring",
    content:
      "Telex can tracks memory, cache hit/miss rates, and latency to ensure smooth data caching and real-time performance.",
    link: "/technology/database-monitoring/redis",
    image: redis,
  },
];
