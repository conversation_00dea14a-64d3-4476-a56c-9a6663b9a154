export const MicroserviceFaq = [
  {
    id: 0,
    question: "Can Telex trace dependencies between microservices?",
    answer: `Yes, Telex provides detailed mapping of microservice dependencies, helping you understand relationships and troubleshoot potential points of failure.`,
  },
  {
    id: 1,
    question: "Does Telex support containerized microservices monitoring?",
    answer: `Absolutely, Telex integrates with Docker and Kubernetes to track container performance, memory allocation, and service health within containerized environments.`,
  },
  {
    id: 2,
    question: "How does Telex handle security within microservices?",
    answer: `Telex tracks access logs, unauthorized access attempts, and error events within microservices to help enforce security compliance and prevent breaches.`,
  },
  {
    id: 3,
    question:
      "Is there a way to monitor resource utilization per microservice?",
    answer: `Yes, Telex offers insights into CPU, memory, and bandwidth usage for each service, helping you allocate resources effectively.`,
  },
];
