export const DatabaseFaq = [
  {
    id: 0,
    question: "Can Telex monitor query execution times?",
    answer: `Yes, Telex provides detailed insights into query execution times, helping you optimize performance and resolve slowdowns.`,
  },
  {
    id: 1,
    question: "Is Telex compatible with cloud-hosted databases?",
    answer: `Absolutely. Telex supports monitoring for cloud-hosted databases across AWS, Azure, Google Cloud, and other providers.`,
  },
  {
    id: 2,
    question: "Does Telex track user activity within the database?",
    answer: `Yes, Telex logs user access, changes, and other activity to enhance database security and compliance.`,
  },
  {
    id: 3,
    question: "Can Telex detect and alert on deadlocks?",
    answer: `Yes, Telex identifies deadlocks, resource conflicts, and other critical issues to ensure smooth database operations.`,
  },
];
