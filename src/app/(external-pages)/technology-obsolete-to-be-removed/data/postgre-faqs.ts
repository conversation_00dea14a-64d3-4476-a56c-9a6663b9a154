export const postgreFaq = [
  {
    id: 0,
    question: "Does Telex support large PostgreSQL databases?",
    answer: `Yes, Telex is built to handle databases of any size.`,
  },
  {
    id: 1,
    question: "Can Telex monitor both on-premises and cloud-hosted PostgreSQL?",
    answer: `Absolutely, Telex supports all deployment environments.`,
  },
  {
    id: 2,
    question: "How does Telex alert me?",
    answer: `You’ll receive alerts via email, SMS, or your preferred communication channel.`,
  },
];
