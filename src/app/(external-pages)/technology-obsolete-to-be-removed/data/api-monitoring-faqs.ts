export const APIMonitoringFaq = [
  {
    id: 0,
    question: "Can Telex monitor rate limits on APIs?",
    answer: `Yes, Telex can track and monitor API rate limits, helping you prevent overuse and optimize endpoint performance.`,
  },
  {
    id: 1,
    question: "Does Telex support real-time notifications for API downtime?",
    answer: `Absolutely. Telex offers instant alerts for API downtime and errors, enabling swift response times.`,
  },
  {
    id: 2,
    question: "Can Telex track dependencies between different APIs?",
    answer: `Yes, Telex maps dependencies, helping you understand the interconnections between APIs and how failures impact your systems.`,
  },
  {
    id: 3,
    question: "Is Telex compatible with WebSocket APIs?",
    answer: `Yes, Telex monitors WebSocket connections, ensuring stability and tracking latency for real-time data delivery.`,
  },
];
