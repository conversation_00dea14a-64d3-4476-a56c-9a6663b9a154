"use server";
import React from "react";
import { Metadata } from "next";
import Link from "next/link";

export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "Workflows - Telex",
    description:
      "Automate your business processes with intelligent workflow solutions. Streamline operations, reduce manual tasks, and improve efficiency with AI-powered automation.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};

const WorkflowsPage = () => {
  const workflowFeatures = [
    {
      id: 1,
      title: "Automated Process Management",
      content:
        "Design and deploy intelligent workflows that automate repetitive tasks, reduce manual errors, and accelerate business processes across your organization.",
    },
    {
      id: 2,
      title: "CI/CD Pipeline Integration",
      content:
        "Seamlessly integrate with your development workflows. Monitor deployments, automate testing, and ensure smooth continuous integration and delivery.",
    },
    {
      id: 3,
      title: "Real-time Workflow Monitoring",
      content:
        "Track workflow performance in real-time with comprehensive analytics. Get instant alerts when processes fail or require attention.",
    },
    {
      id: 4,
      title: "Custom Workflow Builder",
      content:
        "Create custom workflows tailored to your business needs using our intuitive drag-and-drop interface. No coding required.",
    },
  ];

  const workflowSolutions = [
    {
      id: 1,
      title: "GitHub Actions Integration",
      content:
        "Monitor and automate your GitHub workflows with real-time status updates and failure notifications.",
      icon: "🔧",
    },
    {
      id: 2,
      title: "Slack Workflow Automation",
      content:
        "Automate team communications and notifications through intelligent Slack workflow integrations.",
      icon: "💬",
    },
    {
      id: 3,
      title: "Zapier Connectivity",
      content:
        "Connect with thousands of apps through Zapier to create powerful automated workflow chains.",
      icon: "⚡",
    },
    {
      id: 4,
      title: "Webhook Orchestration",
      content:
        "Orchestrate complex workflows using webhooks to trigger actions across multiple systems.",
      icon: "🔗",
    },
    {
      id: 5,
      title: "Database Workflow Triggers",
      content:
        "Trigger workflows based on database changes, ensuring data consistency and automated responses.",
      icon: "🗄️",
    },
    {
      id: 6,
      title: "API Workflow Management",
      content:
        "Manage and monitor API-driven workflows with comprehensive error handling and retry mechanisms.",
      icon: "🌐",
    },
  ];

  const workflowSteps = [
    {
      step: "01",
      title: "Design Your Workflow",
      description:
        "Use our intuitive workflow builder to design automated processes that fit your business needs. Define triggers, actions, and conditions with ease.",
    },
    {
      step: "02",
      title: "Connect Your Tools",
      description:
        "Integrate with your existing tools and platforms. Connect APIs, databases, and third-party services to create comprehensive automation.",
    },
    {
      step: "03",
      title: "Monitor & Optimize",
      description:
        "Track workflow performance with real-time analytics. Receive alerts for failures and continuously optimize your processes for better efficiency.",
    },
  ];

  return (
    <>
      {/* Hero Section */}
      <div className="flex flex-col items-center justify-center text-center py-20 md:py-[120px] bg-gray-50 px-2 md:px-6 lg:px-8">
        <nav className="text-sm text-gray-500 mb-6">
          <Link href="/" className="hover:underline">
            Home
          </Link>{" "}
          &gt; <span className="text-gray-700">Workflows</span>
        </nav>

        <div className="relative mb-4">
          <span className="bg-primary-100/40 text-primary-800 text-sm font-medium py-1 px-3 rounded-full inline-flex items-center">
            <span className="mr-2">New</span>
            <span role="img" aria-label="automation">
              🤖
            </span>
            <span className="sm:ml-2 hidden sm:block">
              Intelligent workflow automation
            </span>
          </span>
        </div>

        <h1 className="w-full lg:w-[60%] xl:w-[50%] text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 lg:leading-[70px]">
          Automate Your Business{" "}
          <span className="text-primary-600">Workflows</span>
        </h1>

        <p className="text-gray-500 text-base sm:text-lg lg:text-xl mb-8 max-w-2xl">
          Streamline operations, reduce manual tasks, and improve efficiency
          with AI-powered workflow automation that adapts to your business
          needs.
        </p>

        <div className="space-x-4">
          <Link
            href="/pricing"
            className="bg-primary-500 text-xs md:text-sm text-white font-semibold py-3 px-6 rounded-lg hover:bg-primary-600 transition duration-300 ease-in-out mb-3"
          >
            Start Automating
          </Link>

          <Link
            href="/contact"
            className="bg-white text-primary-500 text-xs md:text-sm font-semibold py-3 px-6 border border-primary-500 rounded-lg hover:bg-primary-50 transition duration-300 ease-in-out mb-3"
          >
            Learn More
          </Link>
        </div>
      </div>

      {/* Features Section */}
      <div className="px-4 md:px-6 py-[100px] bg-white">
        <div className="max-w-7xl mx-auto text-left">
          <h2 className="sm:w-[70%] md:w-[60%] xl:w-[45%] text-2xl sm:text-3xl lg:text-[32px] font-bold text-gray-900 mb-10 lg:leading-snug">
            Workflow automation solutions designed for modern businesses.
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
            {workflowFeatures.map((feature) => (
              <div key={feature.id}>
                <h3 className="text-[18px] lg:text-xl font-medium text-gray-900">
                  {feature.title}
                </h3>
                <p className="mt-2 text-base sm:text-md text-gray-600">
                  {feature.content}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div className="px-4 md:px-6 py-[100px] bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-[32px] font-bold text-gray-900 mb-4">
              How Workflow Automation Works
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              Get started with workflow automation in three simple steps
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {workflowSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="bg-primary-500 text-white text-2xl font-bold w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {step.title}
                </h3>
                <p className="text-gray-600">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Workflow Solutions Section */}
      <div className="px-4 md:px-6 py-[100px] bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-2xl sm:text-3xl lg:text-[32px] font-bold text-gray-900 mb-4">
              Comprehensive workflow integrations
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              Connect with your favorite tools and platforms to create powerful
              automated workflows
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {workflowSolutions.map((solution) => (
              <div
                key={solution.id}
                className="py-6 bg-white rounded-lg border border-gray-200 hover:border-primary-300 transition-colors duration-200 p-6"
              >
                <div className="text-3xl mb-4">{solution.icon}</div>
                <h3 className="text-lg font-medium mb-2 text-gray-900">
                  {solution.title}
                </h3>
                <p className="text-gray-600">{solution.content}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="px-4 md:px-6 py-[100px] bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-2xl sm:text-3xl lg:text-[32px] font-bold text-gray-900 mb-6">
                Transform your business with intelligent automation
              </h2>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-1">
                    ✓
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      Reduce Manual Work
                    </h3>
                    <p className="text-gray-600">
                      Eliminate repetitive tasks and free up your team to focus
                      on high-value activities.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-1">
                    ✓
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      Improve Accuracy
                    </h3>
                    <p className="text-gray-600">
                      Minimize human errors with automated processes that ensure
                      consistency and reliability.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-1">
                    ✓
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      Scale Operations
                    </h3>
                    <p className="text-gray-600">
                      Handle increased workload without proportional increases
                      in manual effort or resources.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-1">
                    ✓
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      Real-time Insights
                    </h3>
                    <p className="text-gray-600">
                      Get instant visibility into workflow performance with
                      comprehensive analytics and reporting.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-primary-500 to-purple-600 rounded-lg p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Ready to automate?</h3>
              <p className="mb-6">
                Join thousands of businesses already using Telex to streamline
                their workflows and boost productivity.
              </p>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">⚡</span>
                  <span>Quick setup in minutes</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">🔒</span>
                  <span>Enterprise-grade security</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">📈</span>
                  <span>Scalable for any business size</span>
                </div>
              </div>
              <Link
                href="/pricing"
                className="inline-block bg-white text-purple-700 font-semibold py-3 px-6 rounded-lg hover:bg-gray-100 transition duration-300 ease-in-out mt-6"
              >
                Get Started Today
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="px-4 md:px-6 py-[80px] bg-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Start automating your workflows today
          </h2>
          <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
            Transform your business operations with intelligent workflow
            automation. Reduce costs, improve efficiency, and scale your
            operations effortlessly.
          </p>
          <div className="space-x-4">
            <Link
              href="/pricing"
              className="bg-primary-500 text-white font-semibold py-3 px-8 rounded-lg hover:bg-primary-600 transition duration-300 ease-in-out"
            >
              Start Free Trial
            </Link>
            <Link
              href="/contact"
              className="bg-white text-primary-500 font-semibold py-3 px-8 border border-primary-500 rounded-lg hover:bg-primary-50 transition duration-300 ease-in-out"
            >
              Contact Sales
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default WorkflowsPage;
