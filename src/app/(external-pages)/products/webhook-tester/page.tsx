"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Webhook Tester - Telex",
    description:
      "Test and debug webhooks with comprehensive tools. Ensure reliable webhook delivery, monitor performance, and troubleshoot integration issues.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import ImageBg from "../_assets/webhook-testing-bg.svg";
import CaseOne from "../_assets/webhook-case-one.svg";
import CaseTwo from "../_assets/webhook-case-two.svg";
import { webhooktestingtechnologies } from "../data/webhook-testing-technologies";
import { webhookTestingFaq } from "../data/webhook-testing-faqs";

const firstData = [
  {
    id: 1,
    title: "Get Automated Testing",
    content: `Automatically test your webhooks across multiple endpoints, ensuring they work correctly and respond in real time.`,
  },
  {
    id: 2,
    title: "Error Handling",
    content: `Identify and troubleshoot errors in webhook deliveries by monitoring logs and receiving failure alerts.`,
  },
  {
    id: 3,
    title: "Retry Mechanism",
    content: `Telex supports automated retries when webhook deliveries fail, ensuring the message is delivered successfully.`,
  },
  {
    id: 4,
    title: "Payload Validation",
    content: `Validate the payloads of your webhooks to make sure they conform to the expected format and data structures.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "E-commerce Notifications",
    image: CaseOne?.src,
    content: `Ensure successful delivery of webhooks for order confirmations and payment gateways.`,
  },
  {
    id: 2,
    title: "CRM Integrations",
    image: CaseTwo.src,
    content: `Test and monitor webhooks for customer relationship management systems, ensuring data is sent and received correctly.`,
  },
  {
    id: 3,
    title: "IoT Devices",
    image: CaseOne?.src,
    content: `Ensure webhook communication between IoT devices and applications is reliable and without downtime.`,
  },
  {
    id: 4,
    title: "CRM Integrations",
    image: CaseTwo.src,
    content: `Test and monitor webhooks for customer relationship management systems, ensuring data is sent and received correctly.`,
  },
];

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Webhook Testing"
        title="Ensure Every Event Gets Delivered with Precise {{Webhook Testing}}"
        content="Centralize and analyze your application and system logs to gain valuable insights into performance, security, and troubleshooting. With real-time log tracking and alerting, you can quickly detect and resolve issues across your entire infrastructure."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="#"
        learnMoreLink="#"
      />
      <ImageSection image={ImageBg} />
      <ApplicationTools
        heading="Webhook Testing Tool designed for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Teams today rely on over 12 monitoring tools on average to gather and analyze the vast amounts of data generated across modern hybrid and server environments. Managing such a wide range of tools can strain your entire organization.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Monitor your webhooks seamlessly, regardless of the technology."
        items={webhooktestingtechnologies}
      />
      <Guides />
      <Faq faq={webhookTestingFaq} />
      <KeepTrack
        title="Test and Optimize Your Webhooks"
        content="Ensure reliable webhook delivery with Telex. Start monitoring today!"
      />
    </>
  );
};

export default Page;
