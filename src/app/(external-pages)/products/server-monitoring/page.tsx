import React from "react";
import { Metadata } from "next";
import <PERSON> from "../components/single-product/Hero";

export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "Server Monitoring - Telex",
    description:
      "Monitor your servers with comprehensive performance tracking. Ensure optimal server performance, detect issues early, and maintain system reliability.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import ImageSection from "../components/single-product/ImageSection";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import CaseOne from "../_assets/database1.svg";
import CaseTwo from "../_assets/database2.svg";
import ServerBg from "../_assets/server-monitoring-bgs.png";
import { servertechnologies } from "../data/server-technologies";
import Faq from "../components/Faqs";
import { serverMonitoringFaq } from "../data/server-monitoring-faqs";

const firstData = [
  {
    id: 1,
    title: "Real-Time Server Tracking",
    content: `Telex offers continuous monitoring of your server's CPU, memory, and disk usage, ensuring that any performance degradation or failure is immediately noticed and addressed.`,
  },
  {
    id: 2,
    title: "Get Automated monitoring Alerts",
    content: `Set up custom alerts for critical server metrics like CPU usage, memory consumption, and disk space to prevent downtime.`,
  },
  {
    id: 3,
    title: "Historical Data Analysis monitoring",
    content: `Review historical server performance trends to optimize resource usage and plan for scaling effectively.`,
  },
  {
    id: 4,
    title: "Custom Dashboards monitoring",
    content: `Create personalized dashboards to view the most important metrics for your specific server environment.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "E-commerce Website Monitoring",
    image: CaseOne?.src,
    content: `Ensure high server performance for online stores during peak hours and promotions.`,
  },
  {
    id: 2,
    title: "E-commerce Website Monitoring",
    image: CaseTwo.src,
    content: `Ensure high server performance for online stores during peak hours and promotions.`,
  },
  {
    id: 3,
    title: "E-commerce Website Monitoring",
    image: CaseOne?.src,
    content: `Ensure high server performance for online stores during peak hours and promotions.`,
  },
  {
    id: 4,
    title: "E-commerce Website Monitoring",
    image: CaseTwo.src,
    content: `Ensure high server performance for online stores during peak hours and promotions.`,
  },
];

const ServerMonitoring = () => {
  return (
    <>
      <Hero
        breadCumbs="Server Monitoring"
        title="Unlock Peak Performance with Proactive {{Server Monitoring}}"
        content="Keep your servers running smoothly by tracking critical metrics like CPU, memory, and disk usage. Proactively address performance issues, avoid downtime, and ensure optimal server health with real-time monitoring and intelligent alerting."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="#"
        learnMoreLink="#"
      />
      <ImageSection image={ServerBg?.src} />
      <ApplicationTools
        heading="Server Monitoring Tool designed for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`DevOps teams today rely on over 12 monitoring tools on average to gather and analyze the vast amounts of data generated across modern hybrid and cloud environments. Managing such a wide range of tools can strain your entire organization.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Monitor your applications seamlessly, regardless of the technology."
        items={servertechnologies}
      />
      <Guides />
      <Faq faq={serverMonitoringFaq} />
      <KeepTrack
        title="Ensure Your Servers Stay Online"
        content="Sign up for Telex today and keep your servers performing at their best with real-time monitoring and alerts."
      />
    </>
  );
};

export default ServerMonitoring;
