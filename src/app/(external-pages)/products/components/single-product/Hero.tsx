"use client";
import React from "react";
import Link from "next/link";

interface HeroProps {
  breadCumbs: string;
  title: string;
  content: string;
  routeLink: string;
  learnMoreLink?: string;
  routeName?: string;
  learnMoreName?: string;
}

const Hero = (props: HeroProps) => {
  const parseDynamicText = (text: string) => {
    const regex = /\{\{(.*?)\}\}/g;
    const parts = text.split(regex);

    return parts.map((part, index) =>
      index % 2 === 1 ? (
        // If it's an odd index, it's dynamic content inside {{}}
        <span key={index} className="text-primary-500">
          {part}
        </span>
      ) : (
        <span key={index}>{part}</span>
      )
    );
  };

  const handleLearnMoreClick = () => {
    const element = document.getElementById("application-tools");
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };

  //
  return (
    <div className="relative px-4 md:px-6">
      <div className="max-w-7xl mx-auto py-[100px]">
        <div className="text-left">
          {/* Breadcrumb */}
          <nav className="text-sm text-gray-500 mb-10">
            <a href="/" className="hover:underline">
              Home
            </a>{" "}
            &gt;
            <a href="/products" className="hover:underline">
              {" "}
              Products{" "}
            </a>{" "}
            &gt;
            <span className="text-gray-400"> {props?.breadCumbs}</span>
          </nav>

          <h1 className="text-2xl sm:text-4xl md:text-[42px] font-bold lg:w-[55%] md:leading-[60px]">
            {parseDynamicText(props?.title)}
          </h1>

          <p className="mt-4 text-md md:text-lg text-gray-600 md:w-[80%] lg:w-[65%]">
            {props?.content}
          </p>

          <div className="mt-8 flex space-x-4">
            <Link
              href="/auth/sign-up"
              className="bg-primary-500 text-white py-3 px-6 rounded-lg text-xs md:text-base hover:bg-purple-700"
            >
              {props?.routeName}
            </Link>
            <button
              onClick={handleLearnMoreClick}
              className="bg-white text-gray-800 border border-gray-300 text-xs md:text-base py-3 px-6 rounded-lg hover:bg-gray-50"
            >
              {props?.learnMoreName}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
