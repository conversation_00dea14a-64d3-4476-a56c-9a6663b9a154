import React from "react";

interface dataObject {
  title: string;
  content: string;
}

interface Props {
  heading: string;
  items: dataObject[];
}

const ApplicationTools = (props: Props) => {
  return (
    <div id="application-tools" className="px-4 md:px-6 py-[100px]">
      <div className="max-w-7xl mx-auto text-left">
        <h1 className="sm:w-[70%] md:w-[60%] xl:w-[45%] text-2xl sm:text-3xl lg:text-[32px] font-bold text-gray-900 mb-10 lg:leading-snug">
          {props?.heading}
        </h1>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
          {props?.items?.map((item, index) => {
            return (
              <div key={index}>
                <h2 className="text-[18px] lg:text-xl font-medium text-gray-900">
                  {item?.title}
                </h2>
                <p className="mt-2 text-base sm:text-md text-gray-600">
                  {item?.content}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ApplicationTools;
