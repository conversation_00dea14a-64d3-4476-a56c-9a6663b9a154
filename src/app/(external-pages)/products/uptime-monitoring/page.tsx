"use server";
import React from "react";
import { Metadata } from "next";
import UptimeHero from "./components/Hero";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Uptime Monitoring - Telex",
    description:
      "Monitor your website uptime with comprehensive tracking. Ensure maximum availability, detect downtime early, and maintain service reliability.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Features from "./components/Features";
import PainPoints from "./components/PainPoints";
import Agents from "./components/Agents";
import CTA from "./components/cta";
import Resources from "./components/Resources";
import Testimonials from "./components/Testimonials";

const Page = () => {
  return (
    <>
      <UptimeHero />
      <Features />
      <PainPoints />
      <Agents />
      <Testimonials />
      <Resources />
      <CTA />
    </>
  );
};

export default Page;
