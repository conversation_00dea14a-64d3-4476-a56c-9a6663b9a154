"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const metadata = async (): Promise<Metadata> => {
  return {
    title: "Website Testing - Telex",
    description:
      "Test your website performance with comprehensive tools. Monitor functionality, detect issues early, and ensure optimal user experience.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import ImageBg from "../_assets/website-testing-bg.svg";
import CaseOne from "../_assets/website-case-one.svg";
import CaseTwo from "../_assets/website-case-two.svg";
import { websitetestingtechnologies } from "../data/website-testing-technologies";
import { websiteTestingFaq } from "../data/website-testing-faqs";

const firstData = [
  {
    id: 1,
    title: "Use Telex  for Uptime Monitoring",
    content: `Monitor your website's availability and ensure it remains online and accessible 24/7.`,
  },
  {
    id: 2,
    title: "Use Telex for Load Testing",
    content: `Test how your website performs under different traffic loads to prevent slowdowns during peak times.`,
  },
  {
    id: 3,
    title: "Telex Error Monitoring",
    content: `Get notified of critical errors such as 404s, broken links, or server errors that affect the user experience.`,
  },
  {
    id: 4,
    title: "Speed Testing with Telex",
    content: `Track website speed to ensure optimal performance and reduce load times.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "Website Testing",
    image: CaseOne?.src,
    content: `Ensure the site can handle traffic spikes during promotions or sales.`,
  },
  {
    id: 2,
    title: "Corporate Websites",
    image: CaseTwo.src,
    content: `Monitor uptime and performance of large-scale corporate websites.`,
  },
  {
    id: 3,
    title: "SaaS Platform Testing",
    image: CaseOne?.src,
    content: `Ensure the stability and reliability of web-based SaaS platforms.`,
  },
  {
    id: 4,
    title: "CRM Integrations",
    image: CaseTwo.src,
    content: `Test and monitor webhooks for customer relationship management systems, ensuring data is sent and received correctly.`,
  },
];

const Page = () => {
  return (
    <>
      <Hero
        breadCumbs="Website Testing"
        title="Deliver a Flawless User Experience with End-to-End {{Website Testing}}"
        content="Continuously test your website’s functionality, performance, and security. Simulate real-world user interactions and detect potential issues to maintain a fast, secure, and seamless user experience."
        routeName="Sign up"
        learnMoreName="Learn more"
        routeLink="#"
        learnMoreLink="#"
      />
      <ImageSection image={ImageBg} />
      <ApplicationTools
        heading="Application Performance Monitoring Tool designed for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`Teams today rely on over 12 monitoring tools on average to gather and analyze the vast amounts of data generated across modern hybrid and server environments. Managing such a wide range of tools can strain your entire organization.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Monitor your applications seamlessly, regardless of the technology."
        items={websitetestingtechnologies}
      />
      <Guides />
      <Faq faq={websiteTestingFaq} />
      <KeepTrack
        title="Ensure a High-Performance Website"
        content="Monitor and optimize your website’s uptime, speed, and performance. Start your free trial today!"
      />
    </>
  );
};

export default Page;
