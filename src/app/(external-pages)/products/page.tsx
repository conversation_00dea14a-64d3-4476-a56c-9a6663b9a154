"use server";
import React from "react";
import { Metadata } from "next";
import Monitoring from "./components/monitoring";
import Link from "next/link";

export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "Products - Telex",
    description:
      "One platform, thousands of products. Use AI to solve your problems - prebuilt solutions or roll your own. Discover our comprehensive suite of AI-powered tools.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};

const Products = () => {
  return (
    <>
      <div className="flex flex-col items-center justify-center text-center py-20 md:py-[120px] bg-gray-50 px-2 md:px-6 lg:px-8">
        <div className="relative mb-4">
          <span className="bg-gray-100 text-gray-800 text-sm font-medium py-1 px-3 rounded-full inline-flex items-center">
            <span className="mr-2">New</span>
            <span role="img" aria-label="bell">
              🔔
            </span>
            <span className="sm:ml-2 hidden sm:block">
              Catch your downtimes earlier
            </span>
          </span>
        </div>

        <h1 className="w-full lg:w-[60%] xl:w-[50%] text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 lg:leading-[70px]">
          One platform, thousands of products.
        </h1>

        <p className="text-gray-500 text-base sm:text-lg lg:text-xl mb-8">
          Use AI to solve your problems - prebuilt solutions or roll your own
        </p>

        <div className="space-x-4">
          <Link
            href="/pricing"
            className="bg-primary-500 text-xs md:text-sm text-white font-semibold py-3 px-6 rounded-lg hover:bg-purple-700 transition duration-300 ease-in-out mb-3"
          >
            Sign up
          </Link>

          <Link
            href="/auth/sign-up"
            className="bg-white text-primary-500 text-xs md:text-sm font-semibold py-3 px-6 border border-primary-500 rounded-lg hover:bg-purple-50 transition duration-300 ease-in-out mb-3"
          >
            Learn more
          </Link>
        </div>
      </div>

      <Monitoring />
    </>
  );
};

export default Products;
