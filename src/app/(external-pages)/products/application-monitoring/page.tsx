"use server";
import React from "react";
import { Metadata } from "next";
import Faq from "~/telexComponents/homePageFaqs";

export const generateMetadata = async (): Promise<Metadata> => {
  return {
    title: "Application Monitoring - Telex",
    description:
      "Monitor your applications with advanced performance tracking. Ensure optimal application performance, detect issues early, and maintain system reliability.",
    icons: {
      icon: "/TelexIcon.svg",
    },
  };
};
import Hero from "../components/single-product/Hero";
import ApplicationTools from "../components/single-product/ApplicationTools";
import CaseStudy from "../components/single-product/CaseStudies";
import ImageSection from "../components/single-product/ImageSection";
import MonitorComponent from "../components/single-product/Monitor";
import Guides from "../components/single-product/Guides";
import KeepTrack from "../components/single-product/KeepTrack";
import AppPerformanceBg from "../_assets/app-performance-bg.svg";
import CaseOne from "../_assets/case-image-one.svg";
import CaseTwo from "../_assets/case-image-two.svg";
import { technologies } from "../data/technologies";

const firstData = [
  {
    id: 1,
    title: "Use Telex for Performance Monitoring",
    content: `Optimize app performance with real-time insights. Identify bottlenecks, improve speed, and enhance user experience.`,
  },
  {
    id: 2,
    title: "Uptime Monitoring with Telex",
    content: `Ensure your app is always available. Get instant alerts and
              notifications when downtime occurs, and minimize service
              disruptions.`,
  },
  {
    id: 3,
    title: "Telex helps Monitor User Activity",
    content: `Track user engagement. Monitor logins, sign-ups, and other key
              activities to understand user behavior and optimize the user
              experience.`,
  },
  {
    id: 4,
    title: "Error Tracking Easily with Telex",
    content: `Detect and fix errors quickly. Get real-time error tracking,
              alerts to resolve issues before they impact users.`,
  },
];

const casestudyData = [
  {
    id: 1,
    title: "How Telex speeds up production time to market",
    image: CaseOne?.src,
    content: `Limited visibility across systems force teams to constantly
            switch contexts, leaving problems unresolved and hindering the
            pace of innovation.`,
  },
  {
    id: 2,
    title: "How Telex can enhance customer experience",
    image: CaseTwo.src,
    content: `The hight cost of maintaining and integrating multiple observability tools can lead to resource drains and reduced operational efficiency.`,
  },
  {
    id: 3,
    title: "How Telex can optimize resource spending",
    image: CaseOne.src,
    content: `The high cost of maintaining and integrating multiple observability tools can lead to resource drains and reduced operational efficiency.`,
  },
  {
    id: 4,
    title: "How Telex speeds up production time to market",
    image: CaseTwo?.src,
    content: `Limited visibility across systems force teams to constantly
            switch contexts, leaving problems unresolved and hindering the
            pace of innovation.`,
  },
];

const ApplicationMonitoring = () => {
  return (
    <>
      <Hero
        breadCumbs="Application Performance Monitoring"
        title="Advanced {{Application Performance Monitoring}}"
        content="Monitor your app's real-time performance, quickly detect bottlenecks, and improve response times for a favorable user experience."
        routeName="Get Started"
        learnMoreName="Learn more"
        routeLink="/"
        learnMoreLink="#"
      />
      <ImageSection image={AppPerformanceBg} />
      <ApplicationTools
        heading="Application Performance Monitoring Tool designed for you."
        items={firstData}
      />
      <CaseStudy
        tag="Case Studies"
        subheading={`DevOps teams today rely on over 12 monitoring tools on average to
          gather and analyze the vast amounts of data generated across modern
          hybrid and cloud environments. Managing such a wide range of tools can
          strain your entire organization.`}
        items={casestudyData}
      />
      <MonitorComponent
        heading="Monitor your applications seamlessly, regardless of the technology."
        items={technologies}
      />
      <Guides />
      <Faq />
      <KeepTrack
        title="Keep track of your application’s events with Telex application
        performance monitoring tools."
        content="Easily track key incidents, ensure smooth operations, and simplify your
        management tasks. Experience the benefits firsthand and discover a more
        efficient way to optimize your application."
      />
    </>
  );
};

export default ApplicationMonitoring;
