"use client";
import React, { useState } from "react";
import Image from "next/image";

const HowItWorks = () => {
  const [selectedAgent, setSelectedAgent] = useState("how-it-works-1");

  const agents = [
    {
      name: "Choose Your Agent",
      number: "/step-1.png",
      description:
        "Browse Telex’s library and handpick the AI agent tailored to your task, whether it’s social media, website health, or backend errors.",
      image: "how-it-works-1",
    },
    {
      name: "Activate Your Agent",
      number: "/step-2.png",
      description:
        "Simply activate your agent with one click. No complicated setup, no coding required. Your agent is now ready to monitor and assist.",
      image: "how-it-works-2",
    },
    {
      name: "Watch Your Agent Work",
      number: "/step-3.png",
      description:
        "Once activated, your agent handles the heavy lifting, monitoring, analyzing, and sending real-time reports right to your chat or channel.",
      image: "how-it-works-3",
    },
  ];

  return (
    <div className="flex gap-6 mx-auto w-full max-w-[1400px] lg:flex-row flex-col xl:px-14 px-6">
      <div className="lg:w-[35%] w-full">
        <ul className="space-y-6 h-fit">
          {agents.map((agent, index) => (
            <li
              key={index}
              className={`bg-[#FAFAFF] rounded-lg cursor-pointer border border-[#E5E8FF] hover:border-gray-300 
                                relative transition-all duration-500 ease-in-out transform ${selectedAgent === agent.image ? "pb-0" : "pb-6 hover:scale-105"}`}
              onClick={() => setSelectedAgent(agent.image)}
            >
              <div className="flex gap-2 items-center sm:px-6 px-2 pt-6">
                <Image
                  src={agent.number}
                  alt="Step ${agent.number}"
                  width={100}
                  height={100}
                  className={`w-6 h-6 ${selectedAgent === agent.image ? "opacity-100" : "opacity-50"}`}
                />
                <h2
                  className={`sm:text-xl text-lg font-semibold leading-7 
                                    ${selectedAgent === agent.image ? "text-[#101828]" : "text-[#98A2B3]"}`}
                >
                  {agent.name}
                </h2>
              </div>

              {selectedAgent === agent.image && (
                <>
                  <p className="my-4 text-[#475467] text-base font-normal leading-6 sm:px-6 px-2 pb-6">
                    {agent.description}
                  </p>
                  <div className="w-full bg-white bottom-0 absolute h-[17px] rounded-b-lg">
                    <Image
                      src="/card-progress.png"
                      alt="card progress chart"
                      width={260}
                      height={17}
                      className="w-[260px] h-full"
                    />
                  </div>
                </>
              )}
            </li>
          ))}
        </ul>
      </div>
      <div className="lg:w-[65%] w-full">
        <Image
          src={`/${selectedAgent}.png`}
          alt={selectedAgent}
          width={1000}
          height={550}
          quality={100}
          className="w-full sm:h-[386px] h-auto object-cover rounded-lg"
        />
      </div>
    </div>
  );
};

export default HowItWorks;
