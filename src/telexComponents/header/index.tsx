"use client";
/* eslint-disable no-unused-vars */
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import Loading from "~/components/ui/loading";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { ChevronDown } from "lucide-react";
import { usePathname } from "next/navigation";
import ExpandedProduct from "./expanded-product";

//eslint-disable

const Header: React.FC = () => {
  const [windowWidth, setWindowWidth] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [token, setToken] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [expandProduct, setExpandProduct] = useState(false);
  const [isClosingProduct, setIsClosingProduct] = useState(false);
  const pathname = usePathname();

  const logoWhite = "/logoWhite.png";
  const logoBlack = "/logoBlack.png";

  const actionButtons = [
    { label: "Log in", url: "/auth/login" },
    { label: "Start Free Trial", url: "/auth/sign-up" },
  ];

  // Handle window resizing
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // get user token
  useLayoutEffect(() => {
    const user_token = localStorage.getItem("token");
    setToken(user_token);
    setLoading(false);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const hoverTimeout = useRef<any>(null);
  const closeTimeout = useRef<any>(null);
  const mouseLeaveTimeout = useRef<any>(null);

  // product mouse delay
  const handleMouseEnter = () => {
    // Clear any existing timeouts
    if (hoverTimeout.current) {
      clearTimeout(hoverTimeout.current);
      hoverTimeout.current = null;
    }
    if (closeTimeout.current) {
      clearTimeout(closeTimeout.current);
      closeTimeout.current = null;
    }
    if (mouseLeaveTimeout.current) {
      clearTimeout(mouseLeaveTimeout.current);
      mouseLeaveTimeout.current = null;
    }

    // If we're in the process of closing, cancel that
    if (isClosingProduct) {
      setIsClosingProduct(false);
    }

    setExpandProduct(true);
  };

  const handleMouseLeave = () => {
    // Clear any existing timeouts
    if (hoverTimeout.current) {
      clearTimeout(hoverTimeout.current);
    }
    if (closeTimeout.current) {
      clearTimeout(closeTimeout.current);
    }

    // Add a delay before starting the closing animation
    // This gives the user time to move to the expanded menu
    mouseLeaveTimeout.current = setTimeout(() => {
      // Start the closing animation
      setIsClosingProduct(true);

      // Wait for the animation to complete before actually closing
      closeTimeout.current = setTimeout(() => {
        setExpandProduct(false);
        setIsClosingProduct(false);
      }, 500); // Match the CSS transition duration
    }, 200); // 200ms delay before starting to close
  };

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
      if (closeTimeout.current) clearTimeout(closeTimeout.current);
      if (mouseLeaveTimeout.current) clearTimeout(mouseLeaveTimeout.current);
    };
  }, []);

  return (
    <>
      {windowWidth < 1024 && isOpen ? (
        <div className="bg-black opacity-50 fixed top-0 w-screen h-screen z-50 "></div>
      ) : null}
      <nav
        className={`w-full bg-white z-[1000]"  ${isOpen ? "z-50 fixed top-[50%]" : ""} lg:px-0  `}
      >
        <div
          className={`w-full mx-auto flex items-center justify-between h-20 px-6 sm:px-6 md:px-6 fixed left-0 top-0 border-b-[1px] lg:border-[#F2F4F7] right-0 z-40 transition-colors duration-300 ${
            windowWidth < 1024 && isOpen
              ? "bg-[#5A34C6] border-white/20"
              : "bg-white border-[#F2F4F7]"
          }`}
        >
          <div className="w-full flex mx-auto justify-between items-center max-w-[1280px] relative group">
            {/* logo-links */}
            <div className=" flex items-center gap-8 z-50 px-0">
              <div className="shrink-0 flex items-center ">
                {windowWidth < 1024 && isOpen ? (
                  <Link href={"/"}>
                    <Image
                      className="h-8 w-auto "
                      src={logoWhite}
                      alt="Logo"
                      width={100}
                      height={100}
                    />
                  </Link>
                ) : (
                  <Link href={"/"}>
                    <Image
                      className="h-8 w-auto"
                      src={logoBlack}
                      alt="Logo"
                      width={100}
                      height={100}
                    />
                  </Link>
                )}
              </div>

              <div className="h-6 hidden lg:block border-l border-[#F0F2F5]" />

              {/* links */}
              <div
                className="hidden lg:flex sm:space-x-4 md:space-x-6 sm:items-center h-16 "
                onClick={() => {
                  setExpandProduct(false);
                }}
              >
                <Link
                  href={"/products"}
                  className="text-[#344054] hover:text-[#5F5FE1] flex flex-col justify-center item-center h-full px-1 pt-1 text-sm font-medium transition-colors duration-200"
                  onMouseEnter={handleMouseEnter}
                  onMouseLeave={handleMouseLeave}
                  style={{
                    color: pathname?.includes("/products") ? "#5F5FE1" : "",
                  }}
                >
                  Products
                </Link>
                <Link
                  href={"/agents"}
                  className="text-[#344054] hover:text-[#5F5FE1] flex flex-col justify-center item-center h-full px-1 pt-1 text-sm font-medium"
                  style={{
                    color: pathname?.includes("/agents") ? "#5F5FE1" : "",
                  }}
                >
                  Agents
                </Link>
                <Link
                  href={"/workflows"}
                  className="text-[#344054] hover:text-[#5F5FE1] flex flex-col justify-center item-center h-full px-1 pt-1 text-sm font-medium"
                  style={{
                    color: pathname?.includes("/workflows") ? "#5F5FE1" : "",
                  }}
                >
                  Workflows
                </Link>

                <Link
                  href={"/industry"}
                  className="text-[#344054] hover:text-[#5F5FE1] flex flex-col justify-center item-center h-full px-1 pt-1 text-sm font-medium"
                  style={{
                    color: pathname?.includes("/industry") ? "#5F5FE1" : "",
                  }}
                >
                  Industry
                </Link>
                <Link
                  href="https://docs.telex.im/docs/intro"
                  className="text-[#344054] hover:text-[#5F5FE1] flex flex-col justify-center item-center h-full px-1 pt-1 text-sm font-medium"
                  style={{
                    color: pathname?.includes("/docs") ? "#5F5FE1" : "",
                  }}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Docs
                </Link>
                <Link
                  href={"/pricing"}
                  className="text-[#344054] hover:text-[#5F5FE1] flex flex-col justify-center item-center h-full px-1 pt-1 text-sm font-medium"
                  style={{
                    color: pathname?.includes("/pricing") ? "#5F5FE1" : "",
                  }}
                >
                  Pricing
                </Link>
              </div>
            </div>

            {/* Expanded Products - Desktop */}
            {expandProduct && (
              <ExpandedProduct
                setExpandProduct={setExpandProduct}
                handleMouseEnter={handleMouseEnter}
                handleMouseLeave={handleMouseLeave}
                isClosing={isClosingProduct}
              />
            )}

            {/* Buttons */}
            {loading ? (
              <div className="hidden lg:flex">
                <Loading width="30" height="30" color="#7141F8" />
              </div>
            ) : (
              <>
                {token ? (
                  <div className="buttons hidden lg:flex">
                    <Link
                      href="/client"
                      className="text-white inline-flex items-center px-3 py-2 text-sm font-normal bg-primary-500 hover:bg-primary-400 rounded-md"
                    >
                      Go to dashboard
                    </Link>
                  </div>
                ) : (
                  <div className="buttons hidden lg:flex ">
                    {actionButtons.map((button, index) => (
                      <a
                        key={button.url}
                        href={button.url}
                        className={`ml-4 h-11 py-2 rounded-lg text-sm font-medium flex items-center justify-center w-fit text-nowrap ease-in-out duration-300 transition-all ${
                          button.label === "Log in"
                            ? "text-[#344054] hover:text-black"
                            : "text-white px-4 bg-gradient-to-r from-[#8860F8] to-[#7141F8] hover:from-[#7141F8] hover:to-[#8860F8]"
                        }`}
                      >
                        {button.label}
                      </a>
                    ))}
                  </div>
                )}
              </>
            )}

            {/* hamburger */}
            <div
              onClick={toggleMenu}
              className="lg:hidden cursor-pointer h-6 w-6 relative flex justify-center items-center z-50"
            >
              <div
                className={`w-full h-0.5 bg-current absolute transition-transform duration-300 ease-in-out ${
                  isOpen ? "rotate-45 bg-gray-50" : "-translate-y-1.5"
                }`}
              />
              <div
                className={`w-full h-0.5 bg-current absolute transition-opacity duration-300 ease-in-out ${
                  isOpen ? "opacity-0 " : "opacity-100"
                }`}
              />
              <div
                className={`w-full h-0.5 bg-current absolute transition-transform duration-300 ease-in-out ${
                  isOpen ? "-rotate-45 bg-gray-50" : "translate-y-1.5"
                }`}
              />
            </div>
          </div>
        </div>

        {/* mobile menu */}
        {isOpen && (
          <nav className="h-full lg:hidden fixed top-0 inset-x-0 px-4 pt-20 z-30 overflow-y-auto bg-gradient-to-b from-[#5A34C6] to-[#4A2BA3] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-white/20 [&::-webkit-scrollbar-thumb]:rounded-full">
            <div className="flex flex-col justify-between gap-4 py-4">
              <div className=" text-white flex flex-col gap-4">
                <Accordion type="single" collapsible className="w-full px-4">
                  <AccordionItem value="item-1" className="border-white/20">
                    <AccordionTrigger className="font-medium text-[#F8F9FC]">
                      Products
                      <ChevronDown className="transition-transform duration-200 group-[[data-state=open]]:rotate-180" />
                    </AccordionTrigger>
                    <AccordionContent>
                      <div>
                        <div
                          className={`w-full flex items-center justify-between py-2 cursor-pointer text-[#F8F9FC] font-medium leading-normal hover:bg-white/10 rounded-lg mt-1 transition-colors duration-200 ${pathname === "/products/application-monitoring" ? "bg-white/10" : ""}`}
                        >
                          <Link
                            href="/products/application-monitoring"
                            className="w-full flex gap-2 items-center px-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <h2 className="text-sm font-medium leading-normal">
                              Application Performance Monitoring
                            </h2>
                          </Link>
                        </div>
                        <div
                          className={`w-full flex items-center justify-between py-2 cursor-pointer text-[#F8F9FC] font-medium leading-normal hover:bg-white/10 rounded-lg mt-1 transition-colors duration-200 ${pathname === "/products/cloud-monitoring" ? "bg-white/10" : ""}`}
                        >
                          <Link
                            href="/products/cloud-monitoring"
                            className="w-full flex gap-2 items-center px-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <h2 className="text-sm font-medium leading-normal">
                              Cloud Monitoring
                            </h2>
                          </Link>
                        </div>

                        <div
                          className={`w-full flex items-center justify-between py-2 cursor-pointer text-[#F8F9FC] font-medium leading-normal hover:bg-white/10 rounded-lg mt-1 transition-colors duration-200 ${pathname === "/products/database-monitoring" ? "bg-white/10" : ""}`}
                        >
                          <Link
                            href="/products/database-monitoring"
                            className="w-full flex gap-2 items-center px-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <h2 className="text-sm font-medium leading-normal">
                              Database Monitoring
                            </h2>
                          </Link>
                        </div>
                        <div
                          className={`w-full flex items-center justify-between py-2 cursor-pointer text-[#F8F9FC] font-medium leading-normal hover:bg-white/10 rounded-lg mt-1 transition-colors duration-200 ${pathname === "/products/log-analysis" ? "bg-white/10" : ""}`}
                        >
                          <Link
                            href="/products/log-analysis"
                            className="w-full flex gap-2 items-center px-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <h2 className="text-sm font-medium leading-normal">
                              Log Analysis
                            </h2>
                          </Link>
                        </div>
                        <div
                          className={`w-full flex items-center justify-between py-2 cursor-pointer text-[#F8F9FC] font-medium leading-normal hover:bg-white/10 rounded-lg mt-1 transition-colors duration-200 ${pathname === "/products/webhook-testing" ? "bg-white/10" : ""}`}
                        >
                          <Link
                            href="/products/webhook-testing"
                            className="w-full flex gap-2 items-center px-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <h2 className="text-sm font-medium leading-normal">
                              Webhook Testing
                            </h2>
                          </Link>
                        </div>

                        <div
                          className={`w-full flex items-center justify-between py-2 cursor-pointer text-[#F8F9FC] font-medium leading-normal hover:bg-white/10 rounded-lg mt-1 transition-colors duration-200 ${pathname === "/products/website-testing" ? "bg-white/10" : ""}`}
                        >
                          <Link
                            href="/products/website-testing"
                            className="w-full flex gap-2 items-center px-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <h2 className="text-sm font-medium leading-normal">
                              Website Testing
                            </h2>
                          </Link>
                        </div>

                        <div
                          className={`w-full flex items-center justify-between py-2 cursor-pointer text-[#F8F9FC] font-medium leading-normal hover:bg-white/10 rounded-lg mt-1 transition-colors duration-200 ${pathname === "/products/uptime-monitoring" ? "bg-white/10" : ""}`}
                        >
                          <Link
                            href="/products/uptime-monitoring"
                            className="w-full flex gap-2 items-center px-3"
                            onClick={() => setIsOpen(false)}
                          >
                            <h2 className="text-sm font-medium leading-normal">
                              Uptime Monitoring
                            </h2>
                          </Link>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                <div
                  className={`hover:bg-white/10 rounded-lg transition-colors duration-200 ${pathname === "/agents" ? "bg-white/10" : ""}`}
                >
                  <Link
                    href="/agents"
                    onClick={() => setIsOpen(false)}
                    className="block py-2 px-4"
                  >
                    Agents
                  </Link>
                </div>

                <div
                  className={`hover:bg-white/10 rounded-lg transition-colors duration-200 ${pathname === "/technology" ? "bg-white/10" : ""}`}
                >
                  <Link
                    href="/technology"
                    onClick={() => setIsOpen(false)}
                    className="block py-2 px-4"
                  >
                    Technology
                  </Link>
                </div>

                <div
                  className={`hover:bg-white/10 rounded-lg transition-colors duration-200 ${pathname === "/industry" ? "bg-white/10" : ""}`}
                >
                  <Link
                    href="/industry"
                    onClick={() => setIsOpen(false)}
                    className="block py-2 px-4"
                  >
                    Industry
                  </Link>
                </div>

                <div
                  className={`hover:bg-white/10 rounded-lg transition-colors duration-200 ${pathname === "/docs" ? "bg-white/10" : ""}`}
                >
                  <Link
                    href="/docs"
                    onClick={() => setIsOpen(false)}
                    className="block py-2 px-4"
                  >
                    Docs
                  </Link>
                </div>

                <div
                  className={`hover:bg-white/10 rounded-lg transition-colors duration-200 ${pathname === "/pricing" ? "bg-white/10" : ""}`}
                >
                  <Link
                    href="/pricing"
                    onClick={() => setIsOpen(false)}
                    className="block py-2 px-4"
                  >
                    Pricing
                  </Link>
                </div>
              </div>

              {/* mobile buttons */}
              {!loading && token ? (
                <Link
                  href="/client"
                  className="mx-4 text-white flex items-center justify-center py-3 text-sm font-medium bg-white/20 hover:bg-white/30 rounded-lg transition-colors duration-200"
                >
                  Go to dashboard
                </Link>
              ) : (
                <div className="flex flex-col gap-3 px-4 mt-4">
                  {actionButtons.map((button) => (
                    <a
                      key={button.url}
                      href={button.url}
                      className={`py-3 rounded-lg text-sm font-medium flex items-center justify-center w-full transition-colors duration-200 ${
                        button.label === "Log in"
                          ? "text-white bg-white/20 hover:bg-white/30"
                          : "text-[#5A34C6] bg-white hover:bg-gray-100"
                      }`}
                    >
                      {button.label}
                    </a>
                  ))}
                </div>
              )}
            </div>
          </nav>
        )}
      </nav>
    </>
  );
};

export default Header;
