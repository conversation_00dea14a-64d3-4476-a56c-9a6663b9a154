import { useContext } from "react";
import { DataContext } from "~/store/GlobalState";

/**
 * Hook to access subscription plans from global state
 * @returns Object containing subscription plans and loading state
 */
export const useGetSubscriptionPlans = () => {
  const { state } = useContext(DataContext);

  return {
    subscriptionPlans: state?.subscriptionPlans,
    isLoading: !state?.subscriptionPlans,
  };
};

export const useGetCurrentSubscription = () => {
  const { state } = useContext(DataContext);

  return {
    currentSubscription: state?.currentSubscription,
    isLoading: !state?.currentSubscription,
  };
};

/**
 * Type definitions for subscription plans based on API response structure
 */
export interface SubscriptionPlan {
  id: string;
  name: string;
  fee: number;
  max_channels: number; // -1 means unlimited
  max_users: number; // -1 means unlimited
  max_notifications: number; // -1 means unlimited
  can_upgrade_notifications: boolean;
  can_add_unlimited_channels: boolean;
  can_add_unlimited_users: boolean;
  is_for_individuals: boolean;
  is_for_small_business: boolean;
  is_for_large_enterprise: boolean;
  created_at: string;
  credits: number;
  updated_at: string;
}

export interface SubscriptionPlansResponse {
  data: SubscriptionPlan[];
  success: boolean;
  message?: string;
}

/**
 * Utility functions for working with subscription plans
 */
export const subscriptionPlanUtils = {
  /**
   * Format limit values (-1 means unlimited)
   */
  formatLimit: (value: number, unit: string = ""): string => {
    return value === -1
      ? "No user limit"
      : `${value.toLocaleString()}${unit ? " " + unit : ""}`;
  },

  /**
   * Get plan target audience
   */
  getPlanAudience: (plan: SubscriptionPlan): string => {
    if (plan.is_for_individuals) return "Perfect for individuals";
    if (plan.is_for_small_business) return "Ideal for small businesses";
    if (plan.is_for_large_enterprise) return "Built for large enterprises";
    return "";
  },

  /**
   * Check if plan is popular (Business plan)
   */
  isPopularPlan: (plan: SubscriptionPlan): boolean => {
    return plan.name.toLowerCase() === "business";
  },

  /**
   * Get plan features as array
   */
  getPlanFeatures: (plan: SubscriptionPlan): string[] => {
    const features: string[] = [];

    features.push(subscriptionPlanUtils.formatLimit(plan.max_users, "users"));
    features.push(
      subscriptionPlanUtils.formatLimit(plan.max_channels, "channels")
    );
    features.push(
      subscriptionPlanUtils.formatLimit(plan.max_notifications, "notifications")
    );

    if (plan.credits > 0) {
      features.push(`${plan.credits} AI Credits`);
    }

    if (plan.can_upgrade_notifications) {
      features.push("Upgrade notifications available");
    } else {
      features.push("Unlimited notifications");
    }

    return features;
  },

  /**
   * Sort plans by price (Free first, then by fee)
   */
  sortPlansByPrice: (plans: SubscriptionPlan[]): SubscriptionPlan[] => {
    return [...plans].sort((a, b) => {
      if (a.fee === 0) return -1;
      if (b.fee === 0) return 1;
      return a.fee - b.fee;
    });
  },
};
