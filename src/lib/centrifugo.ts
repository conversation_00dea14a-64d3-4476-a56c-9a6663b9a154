// lib/centrifugo.ts
import axios from "axios";
import { Centrifuge, Subscription } from "centrifuge";

const CONNECT_URL = process.env.NEXT_PUBLIC_CONNECT_URL!;
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL!;

export const getConnectionToken = async (): Promise<string> => {
  const token = localStorage.getItem("token") || "";
  const response = await axios.get<{ data: { token: string } }>(
    `${BASE_URL}/token/connection`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
  return response.data.data.token;
};

export const getSubscriptionToken = async (
  channel: string
): Promise<string> => {
  const token = localStorage.getItem("token") || "";
  const response = await axios.post<{ data: { token: string } }>(
    `${BASE_URL}/token/subscription`,
    { channel },
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
  return response.data.data.token;
};

type CreateSubscriptionArgs = {
  channel: string;
  onMessage: any;
};

type SubscriptionResponse = {
  centrifugeClient: Centrifuge;
  sub: Subscription;
  unsubscribe: () => void;
};

export const createCentrifugoSubscription = async ({
  channel,
  onMessage,
}: CreateSubscriptionArgs): Promise<SubscriptionResponse> => {
  const centrifugeClient: any = new Centrifuge(CONNECT_URL, {
    getToken: getConnectionToken,
    debug: true,
  });

  centrifugeClient.on("connect", () => {
    console.log("Connected to Centrifugo");
  });

  centrifugeClient.on("disconnect", (ctx: any) => {
    console.warn("Disconnected from Centrifugo:", ctx.reason);
  });

  const getPersonalChannelSubscriptionToken = async (): Promise<string> => {
    return getSubscriptionToken(channel);
  };

  const sub = centrifugeClient.newSubscription(channel, {
    getToken: getPersonalChannelSubscriptionToken,
  });

  sub.on("subscribed", () => {
    console.log(`Subscribed to ${channel}`);
  });

  sub.on("publication", (ctx: { data: any }) => {
    onMessage(ctx.data);
  });

  sub.on("error", (ctx: { message: string }) => {
    console.error(`Subscription error: ${ctx.message}`);
  });

  centrifugeClient.connect();
  sub.subscribe();

  return {
    centrifugeClient,
    sub,
    unsubscribe: () => {
      sub.unsubscribe();
      centrifugeClient.disconnect();
    },
  };
};
